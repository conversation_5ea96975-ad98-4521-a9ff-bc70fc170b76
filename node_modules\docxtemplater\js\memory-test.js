"use strict";

var fs = require("fs");
var PizZip = require("pizzip");
var Docxtemplater = require("./docxtemplater.js");
var zip = new PizZip(fs.readFileSync("examples/memory-stress.docx"));
var doc = new Docxtemplater(zip, {
  paragraphLoop: true
});
var a = [];
for (var i = 0, len = 500; i < len; i++) {
  var b = [];
  for (var j = 0, len2 = 500; j < len2; j++) {
    b.push({
      title: i + j,
      c: [{
        content: "Hi"
      }, {
        content: "Ho"
      }]
    });
  }
  a.push({
    b: b
  });
}
doc.render({
  a: a
});
var buf = doc.toBuffer();
var minSize = 500;
if (buf.length < minSize * 1000 * 1000) {
  throw new Error("The output document should be at least ${minSize} MB");
}
// eslint-disable-next-line no-console
console.log("memory-test buffer length : ", buf.length);