!function(){var e={60:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var a=r(536),s=r(207).isContent,l=r(946),u=l.throwRawTagShouldBeOnlyTextInParagraph,p=l.getInvalidRawXMLValueException,c=r(899),f="rawxml";function h(e){for(var t=e.part,r=e.left,n=e.right,o=e.postparsed,i=e.index,a=o.slice(r+1,n),l=0,p=a.length;l<p;l++)if(l!==i-r-1){var c=a[l];s(c)&&u({paragraphParts:a,part:t})}return t}var d=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="RawXmlModule",this.prefix="@"},t=[{key:"optionsTransformer",value:function(e,t){return this.fileTypeConfig=t.fileTypeConfig,e}},{key:"matchers",value:function(){return[[this.prefix,f]]}},{key:"postparse",value:function(e){return a.expandToOne(e,{moduleName:f,getInner:h,expandTo:this.fileTypeConfig.tagRawXml,error:{message:"Raw tag not in paragraph",id:"raw_tag_outerxml_invalid",explanation:function(e){return'The tag "'.concat(e.value,'" is not inside a paragraph, putting raw tags inside an inline loop is disallowed.')}}})}},{key:"render",value:function(e,t){if(e.module!==f)return null;var r,n=[];try{null!=(r=t.scopeManager.getValue(e.value,{part:e}))||(r=t.nullGetter(e))}catch(e){return n.push(e),{errors:n}}return"string"==typeof(r=r||"")?{value:r}:{errors:[p({tag:e.value,value:r,offset:e.offset})]}}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return c(new d)}},183:function(e){function t(e){var t,r,n,o,i=0,a=e.length;for(n=0;n<a;n++)55296==(64512&(t=e.charCodeAt(n)))&&n+1<a&&56320==(64512&(r=e.charCodeAt(n+1)))&&(t=65536+(t-55296<<10)+(r-56320),n++),i+=t<128?1:t<2048?2:t<65536?3:4;var s=new Uint8Array(i);for(o=0,n=0;o<i;n++)55296==(64512&(t=e.charCodeAt(n)))&&n+1<a&&56320==(64512&(r=e.charCodeAt(n+1)))&&(t=65536+(t-55296<<10)+(r-56320),n++),t<128?s[o++]=t:t<2048?(s[o++]=192|t>>>6,s[o++]=128|63&t):t<65536?(s[o++]=224|t>>>12,s[o++]=128|t>>>6&63,s[o++]=128|63&t):(s[o++]=240|t>>>18,s[o++]=128|t>>>12&63,s[o++]=128|t>>>6&63,s[o++]=128|63&t);return s}e.exports=function(e,r){for(var n=0,o=r.modules;n<o.length;n++)e=o[n].postrender(e,r);for(var i=0,a=r.joinUncorrupt(e,r),s="",l=0,u=[],p=0,c=a.length;p<c;p++){var f=a[p];if(f.length+l>65536){var h=t(s);i+=h.length,u.push(h),s=""}s+=f,l+=f.length,delete a[p]}var d=t(s);i+=d.length,u.push(d);for(var v=new Uint8Array(i),g=0,m=0;m<u.length;m++){for(var y=u[m],b=0;b<y.length;++b)v[b+g]=y[b];g+=y.length}return v}},201:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var a=r(798),s=r(207),l=s.getLeft,u=s.getRight,p=s.pushArray,c=r(899),f=r(536).getExpandToDefault,h=r(946),d=h.getUnmatchedLoopException,v=h.getClosingTagNotMatchOpeningTag,g=h.getUnbalancedLoopException;function m(e){switch(e.location){case"start":return 1;case"end":return-1}}function y(e,t){return null!=e&&null!=t&&("start"===e.part.location&&"end"===t.part.location&&e.part.value===t.part.value||""===t.part.value)}function b(e){for(var t=0,r=[];t<e.length;){var n=e[t].part;if("end"===n.location){if(0===t)return e.splice(0,1),r.push(d(n)),{traits:e,errors:r};var o=t,i=t-1,a=1;if(y(e[i],e[o]))return e.splice(o,1),e.splice(i,1),{errors:r,traits:e};for(;a<50;){var s=e[i-a],l=e[o+a];if(y(s,e[o]))return e.splice(o,1),e.splice(i-a,1),{errors:r,traits:e};if(y(e[i],l))return e.splice(o+a,1),e.splice(i,1),{errors:r,traits:e};a++}return r.push(v({tags:[e[i].part,e[o].part]})),e.splice(o,1),e.splice(i,1),{traits:e,errors:r}}t++}for(var u=0;u<e.length;u++){var p=e[u].part;r.push(d(p))}return{traits:[],errors:r}}var x=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="ExpandPairTrait"},t=[{key:"optionsTransformer",value:function(e,t){return t.options.paragraphLoop&&p(t.fileTypeConfig.expandTags,t.fileTypeConfig.onParagraphLoop),this.expandTags=t.fileTypeConfig.expandTags,e}},{key:"postparse",value:function(e,t){var r=this,n=t.getTraits,o=t.postparse,i=t.fileType,s=n("expandPair",e);s=s.map((function(e){return e||[]}));var c=function(e){var t={},r=[],n=[],o=[];for(p(o,e);o.length>0;){var i=b(o);p(r,i.errors),o=i.traits}if(r.length>0)return{pairs:n,errors:r};for(var a=0,s=0;s<e.length;s++){var l=e[s],u=m(l.part);if(a+=u,1===u)t[a]=l;else{var c=t[a+1];0===a&&n.push([c,l])}a=a>=0?a:0}return{pairs:n,errors:r}}(s=a(s)),h=c.pairs,d=c.errors,v=0,y=null,x=h.map((function(t){var n,o,a=t[0].part.expandTo;if("auto"===a&&"text"!==i){var s=f(e,t,r.expandTags);s.error&&d.push(s.error),a=s.value}if(!a||"text"===i){var p=t[0].offset,c=t[1].offset;return p<v&&!r.docxtemplater.options.syntax.allowUnbalancedLoops&&d.push(g(t,y)),y=t,v=c,[p,c]}try{n=l(e,a,t[0].offset)}catch(e){d.push(e)}try{o=u(e,a,t[1].offset)}catch(e){d.push(e)}return n<v&&!r.docxtemplater.options.syntax.allowUnbalancedLoops&&d.push(g(t,y)),v=o,y=t,[n,o]}));if(d.length>0)return{postparsed:e,errors:d};var w,T=0;return{postparsed:e.reduce((function(t,r,n){var i=T<h.length&&x[T][0]<=n&&n<=x[T][1],a=h[T],s=x[T];if(!i)return t.push(r),t;if(s[0]===n&&(w=[]),a[0].offset!==n&&a[1].offset!==n&&w.push(r),s[1]===n){var l=e[a[0].offset];l.subparsed=o(w,{basePart:l}),l.endLindex=a[1].part.lIndex,delete l.location,delete l.expandTo,t.push(l),T++;for(var u=x[T];u&&u[0]<n;)T++,u=x[T]}return t}),[]),errors:d}}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return c(new x)}},207:function(e,t,r){function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var o=r(673),i=o.DOMParser,a=o.XMLSerializer,s=r(946).throwXmlTagNotFound,l=r(320),u=l.last,p=l.first;function c(e){return{get:function(t){return"."===e?t:t?t[e]:t}}}var f={},h=[["&","&amp;"],["<","&lt;"],[">","&gt;"],['"',"&quot;"],["'","&apos;"]],d=h.map((function(e){var t,r,o=(r=2,function(e){if(Array.isArray(e))return e}(t=e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(t,r)||function(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],a=o[1];return{rstart:new RegExp(a,"g"),rend:new RegExp(i,"g"),start:a,end:i}})),v=new RegExp(String.fromCharCode(160),"g");function g(e,t){return e==="</"+t+">"}function m(e,t){return 0===e.indexOf("<"+t)&&-1!==[">"," ","/"].indexOf(e[t.length+1])}function y(e,t,r){"string"==typeof t&&(t=[t]);for(var n=1,o=r,i=e.length;o<i;o++)for(var a=e[o],s=0,l=t;s<l.length;s++){var u=l[s];if(g(a.value,u)&&n--,m(a.value,u)&&n++,0===n)return o}return null}function b(e,t,r){"string"==typeof t&&(t=[t]);for(var n=1,o=r;o>=0;o--)for(var i=e[o],a=0,s=t;a<s.length;a++){var l=s[a];if(m(i.value,l)&&n--,g(i.value,l)&&n++,0===n)return o}return null}var x=/[\x00-\x08\x0B\x0C\x0E-\x1F]/g;e.exports={endsWith:function(e,t){return-1!==e.indexOf(t,e.length-t.length)},startsWith:function(e,t){return e.substring(0,t.length)===t},isContent:function(e){var t=e.type,r=e.position;return"placeholder"===t||"content"===t&&"insidetag"===r},isParagraphStart:function(e){var t=e.type,r=e.tag,n=e.position;return-1!==["w:p","a:p"].indexOf(r)&&"tag"===t&&"start"===n},isParagraphEnd:function(e){var t=e.type,r=e.tag,n=e.position;return-1!==["w:p","a:p"].indexOf(r)&&"tag"===t&&"end"===n},isTagStart:function(e,t){var r=t.type,n=t.tag,o=t.position;return"tag"===r&&n===e&&("start"===o||"selfclosing"===o)},isTagEnd:function(e,t){var r=t.type,n=t.tag,o=t.position;return"tag"===r&&n===e&&"end"===o},isTextStart:function(e){var t=e.type,r=e.position;return e.text&&"tag"===t&&"start"===r},isTextEnd:function(e){var t=e.type,r=e.position;return e.text&&"tag"===t&&"end"===r},isStarting:m,isEnding:g,isModule:function(e,t){var r=e.module,n=e.type;return t instanceof Array||(t=[t]),"placeholder"===n&&-1!==t.indexOf(r)},uniq:function(e){for(var t={},r=[],n=0,o=e.length;n<o;++n)t[e[n]]||(t[e[n]]=!0,r.push(e[n]));return r},getDuplicates:function(e){for(var t=[],r={},n=[],o=0,i=e.length;o<i;++o)r[e[o]]?t.push(e[o]):(r[e[o]]=!0,n.push(e[o]));return t},chunkBy:function(e,t){for(var r=[[]],n=0;n<e.length;n++){var o=e[n],i=r[r.length-1],a=t(o);"start"===a?r.push([o]):"end"===a?(i.push(o),r.push([])):i.push(o)}for(var s=[],l=0;l<r.length;l++){var u=r[l];u.length>0&&s.push(u)}return s},last:u,first:p,xml2str:function(e){return(new a).serializeToString(e).replace(/xmlns(:[a-z0-9]+)?="" ?/g,"")},str2xml:function(e){return 65279===e.charCodeAt(0)&&(e=e.substr(1)),(new i).parseFromString(e,"text/xml")},getRightOrNull:y,getRight:function(e,t,r){var n=y(e,t,r);if(null!==n)return n;s({position:"right",element:t,parsed:e,index:r})},getLeftOrNull:b,getLeft:function(e,t,r){var n=b(e,t,r);if(null!==n)return n;s({position:"left",element:t,parsed:e,index:r})},pregMatchAll:function(e,t){for(var r,n=[];null!=(r=e.exec(t));)n.push({array:r,offset:r.index});return n},convertSpaces:function(e){return e.replace(v," ")},charMapRegexes:d,hasCorruptCharacters:function(e){return x.lastIndex=0,x.test(e)},removeCorruptCharacters:function(e){return"string"!=typeof e&&(e=String(e)),e.replace(x,"")},getDefaults:function(){return{errorLogging:"json",stripInvalidXMLChars:!1,paragraphLoop:!1,nullGetter:function(e){return e.module?"":"undefined"},xmlFileNames:["[Content_Types].xml"],parser:c,linebreaks:!1,fileTypeConfig:null,delimiters:{start:"{",end:"}"},syntax:{changeDelimiterPrefix:"="}}},wordToUtf8:function(e){for(var t=d.length-1;t>=0;t--){var r=d[t];e=e.replace(r.rstart,r.end)}return e},utf8ToWord:function(e){var t;e=e.toString();for(var r=0,n=d.length;r<n;r++)t=d[r],e=e.replace(t.rend,t.start);return e},concatArrays:function(e){for(var t=[],r=0;r<e.length;r++)for(var n=e[r],o=0;o<n.length;o++){var i=n[o];t.push(i)}return t},pushArray:function(e,t){if(!t)return e;for(var r=0,n=t.length;r<n;r++)e.push(t[r]);return e},invertMap:function(e){var t={};for(var r in e){var n=e[r];t[n]||(t[n]=[]),t[n].push(r)}return t},charMap:h,getSingleAttribute:function(e,t){var r=e.indexOf(" ".concat(t,'="'));if(-1===r)return null;var n=e.substr(r).search(/["']/)+r,o=e.substr(n+1).search(/["']/)+n;return e.substr(n+1,o-n)},setSingleAttribute:function(e,t,r){var n;if(f[t]?n=f[t]:(n=new RegExp("(<.* ".concat(t,'=")([^"]*)(".*)$')),f[t]=n),n.test(e))return e.replace(n,"$1".concat(r,"$3"));var o=e.lastIndexOf("/>");return-1===o&&(o=e.lastIndexOf(">")),e.substr(0,o)+" ".concat(t,'="').concat(r,'"')+e.substr(o)},isWhiteSpace:function(e){return/^[ \n\r\t]+$/.test(e)},stableSort:function(e,t){return e.map((function(e,t){return{item:e,index:t}})).sort((function(e,r){return t(e.item,r.item)||e.index-r.index})).map((function(e){return e.item}))}}},208:function(e,t,r){var n=r(207),o=n.startsWith,i=n.endsWith,a=n.isStarting,s=n.isEnding,l=n.isWhiteSpace,u=r(322);e.exports=function(e,t){var r=t.fileTypeConfig.tagShouldContain||[],n="",p=-1;-1!==u.docx.indexOf(t.contentType)&&(e=function(e){for(var t="",r=0,n=e.length;r<n;r++){var a=e[r];l(a)||o(a,"<w:bookmarkEnd")||(i(t,"</w:tbl>")&&(o(a,"<w:p")||o(a,"<w:tbl")||o(a,"<w:sectPr")||(a="<w:p/>".concat(a))),t=a,e[r]=a)}return e}(e));for(var c=-1,f=0,h=r.length;f<h;f++)for(var d=r[f],v=d.tag,g=d.shouldContain,m=d.value,y=d.drop,b=d.dropParent,x=0,w=e.length;x<w;x++){var T=e[x];if(p===f){if(s(T,v))if(p=-1,b){for(var P=-1,O=c;O>0;O--)if(a(e[O],b)){P=O;break}for(var S=P;S<=e.length;S++){if(s(e[S],b)){e[S]="";break}e[S]=""}}else{for(var j=c;j<=x;j++)e[j]="";y||(e[x]=n+m+T)}n+=T;for(var E=0,C=g.length;E<C;E++){var k=g[E];if(a(T,k)){p=-1;break}}}-1===p&&a(T,v)&&-1===T.substr(1).indexOf("<")&&("/"===T[T.length-2]?e[x]="":(c=x,p=f,n=T))}return e}},245:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var a=r(207),s=a.pushArray,l=a.wordToUtf8,u=a.convertSpaces,p=r(367),c=r(263),f=r(690),h=r(789),d=r(183),v=r(945),g=r(208);e.exports=function(){return e=function e(t,r){for(var n in function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.cachedParsers={},this.content=t,r)this[n]=r[n];this.setModules({inspect:{filePath:r.filePath}})},t=[{key:"resolveTags",value:function(e){var t=this;this.tags=e;var r=this.getOptions(),n=this.filePath;r.scopeManager=this.scopeManager,r.resolve=v;var o=[];return Promise.all(this.modules.map((function(e){return Promise.resolve(e.preResolve(r)).catch((function(e){o.push(e)}))}))).then((function(){if(0!==o.length)throw o;return v(r).then((function(e){var o=e.resolved,i=e.errors;if(0!==(i=i.map((function(e){var t;return e instanceof Error||(e=new Error(e)),(t=e).properties||(t.properties={}),e.properties.file=n,e}))).length)throw i;return Promise.all(o).then((function(e){return r.scopeManager.root.finishedResolving=!0,r.scopeManager.resolved=e,t.setModules({inspect:{resolved:e,filePath:n}}),e}))})).catch((function(e){throw t.errorChecker(e),e}))}))}},{key:"getFullText",value:function(){return e=this.content,t=this.fileTypeConfig.tagsXmlTextArray,r=p(e,t).matches.map((function(e){return e.array[2]})),l(u(r.join("")));var e,t,r}},{key:"setModules",value:function(e){for(var t=0,r=this.modules;t<r.length;t++)r[t].set(e)}},{key:"preparse",value:function(){this.allErrors=[],this.xmllexed=c.xmlparse(this.content,{text:this.fileTypeConfig.tagsXmlTextArray,other:this.fileTypeConfig.tagsXmlLexedArray}),this.setModules({inspect:{filePath:this.filePath,xmllexed:this.xmllexed}});var e=c.parse(this.xmllexed,this.delimiters,this.syntax,this.fileType),t=e.lexed,r=e.errors;s(this.allErrors,r),this.lexed=t,this.setModules({inspect:{filePath:this.filePath,lexed:this.lexed}});var n=this.getOptions();this.lexed=f.preparse(this.lexed,this.modules,n)}},{key:"parse",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).noPostParse;this.setModules({inspect:{filePath:this.filePath}});var t=this.getOptions();return this.parsed=f.parse(this.lexed,this.modules,t),this.setModules({inspect:{filePath:this.filePath,parsed:this.parsed}}),e?this:this.postparse()}},{key:"postparse",value:function(){var e=this.getOptions(),t=f.postparse(this.parsed,this.modules,e),r=t.postparsed,n=t.errors;return this.postparsed=r,this.setModules({inspect:{filePath:this.filePath,postparsed:this.postparsed}}),s(this.allErrors,n),this.errorChecker(this.allErrors),this}},{key:"errorChecker",value:function(e){for(var t=0,r=e;t<r.length;t++){var n=r[t];n.properties||(n.properties={}),n.properties.file=this.filePath}for(var o=0,i=this.modules;o<i.length;o++)e=i[o].errorsTransformer(e)}},{key:"baseNullGetter",value:function(e,t){var r=this,n=this.modules.reduce((function(n,o){return null!=n?n:o.nullGetter(e,t,r)}),null);return null!=n?n:this.nullGetter(e,t)}},{key:"getOptions",value:function(){return{compiled:this.postparsed,cachedParsers:this.cachedParsers,tags:this.tags,modules:this.modules,parser:this.parser,contentType:this.contentType,relsType:this.relsType,baseNullGetter:this.baseNullGetter.bind(this),filePath:this.filePath,fileTypeConfig:this.fileTypeConfig,fileType:this.fileType,linebreaks:this.linebreaks,stripInvalidXMLChars:this.stripInvalidXMLChars}}},{key:"render",value:function(e){this.filePath=e;var t=this.getOptions();t.resolved=this.scopeManager.resolved,t.scopeManager=this.scopeManager,t.render=h,t.joinUncorrupt=g;var r=h(t),n=r.errors,o=r.parts;return n.length>0?(this.allErrors=n,this.errorChecker(n),this):(this.content=d(o,t),this.setModules({inspect:{filePath:this.filePath,content:this.content}}),this)}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}()},263:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var u=r(946),p=u.getUnclosedTagException,c=u.getUnopenedTagException,f=u.getDuplicateOpenTagException,h=u.getDuplicateCloseTagException,d=u.throwMalformedXml,v=u.throwXmlInvalid,g=u.XTTemplateError,m=r(207),y=m.isTextStart,b=m.isTextEnd,x=m.wordToUtf8,w=m.pushArray;function T(e,t){return e[0]<=t.offset&&t.offset<e[1]}function P(e,t){return y(e)?(t&&d(),!0):b(e)?(t||d(),!1):t}function O(e){var t="",r=1,n=e.indexOf(" ");return"/"===e[e.length-2]?(t="selfclosing",-1===n&&(n=e.length-2)):"/"===e[1]?(r=2,t="end",-1===n&&(n=e.length-1)):(t="start",-1===n&&(n=e.length-1)),{tag:e.slice(r,n),position:t}}function S(e,t){return-1===e&&-1===t?0:e===t?1:-1===e||-1===t?t<e?2:3:e<t?2:3}function j(e){var t=e.split(" ");if(2!==t.length){var r=new g("New Delimiters cannot be parsed");throw r.properties={id:"change_delimiters_invalid",explanation:"Cannot parser delimiters"},r}var n=o(t,2),i=n[0],a=n[1];if(0===i.length||0===a.length){var s=new g("New Delimiters cannot be parsed");throw s.properties={id:"change_delimiters_invalid",explanation:"Cannot parser delimiters"},s}return[i,a]}function E(e,t,r){var n=e.map((function(e){return e.value})).join(""),i=function(e,t,r){var n=[],i=t.start,a=t.end,s=-1,l=!1;if(null==i&&null==a)return[];for(;;){var u=e.indexOf(i,s+1),p=e.indexOf(a,s+1),c=null,f=void 0,h=S(u,p);switch(1===h&&(h=l?3:2),h){case 0:return n;case 3:l=!1,s=p,c="end",f=a.length;break;case 2:l=!0,s=u,c="start",f=i.length}if(r.changeDelimiterPrefix&&2===h&&e[s+i.length]===r.changeDelimiterPrefix){n.push({offset:u,position:"start",length:i.length,changedelimiter:!0});var d=e.indexOf(r.changeDelimiterPrefix,s+i.length+1),v=e.indexOf(a,d+1);n.push({offset:v,position:"end",length:a.length,changedelimiter:!0});var g=o(j(e.substr(s+i.length+1,d-s-i.length-1)),2);i=g[0],a=g[1],s=v}else n.push({offset:s,position:c,length:f})}}(n,t,r),a=0,l=e.map((function(e){return{offset:(a+=e.value.length)-e.value.length,lIndex:e.lIndex}})),u=function(e,t,r){var n,o=[],i=!1,a={offset:0},l=e.reduce((function(e,l){var u=l.position,d=l.offset,v=a.offset,g=a.length;if(n=t.substr(v,d-v),i&&"start"===u){if(v+g===d&&(n=t.substr(v,d-v+g+4),!r.allowUnclosedTag))return o.push(f({xtag:n,offset:v})),a=l,e.push(s(s({},l),{},{error:!0})),e;if(!r.allowUnclosedTag)return o.push(p({xtag:x(n),offset:v})),a=l,e.push(s(s({},l),{},{error:!0})),e;e.pop()}return i||"end"!==u?(i="start"===u,a=l,e.push(l),e):r.allowUnopenedTag?e:v+g===d?(n=t.substr(v-4,d-v+g+4),o.push(h({xtag:n,offset:v})),a=l,e.push(s(s({},l),{},{error:!0})),e):(o.push(c({xtag:n,offset:d})),a=l,e.push(s(s({},l),{},{error:!0})),e)}),[]);if(i){var u=a.offset;n=t.substr(u,t.length-u),r.allowUnclosedTag?l.pop():o.push(p({xtag:x(n),offset:u}))}return{delimiterWithErrors:l,errors:o}}(i,n,r),d=u.delimiterWithErrors,v=u.errors,g=0,m=0,y=l.map((function(t,r){for(var n=t.offset,o=[n,n+e[r].value.length],i=e[r].value,a=[];m<d.length&&T(o,d[m]);)a.push(d[m]),m++;var s=[],l=0;g>0&&(l=g,g=0);for(var u=0;u<a.length;u++){var p=a[u],c=i.substr(l,p.offset-n-l);if(p.changedelimiter)"start"===p.position?c.length>0&&s.push({type:"content",value:c}):l=p.offset-n+p.length;else{c.length>0&&(s.push({type:"content",value:c}),l+=c.length);var f={type:"delimiter",position:p.position,offset:l+n};s.push(f),l=p.offset-n+p.length}}g=l-i.length;var h=i.substr(l);return h.length>0&&s.push({type:"content",value:h}),s}),this);return{parsed:y,errors:v}}function C(e){return"content"===e.type&&"insidetag"===e.position}e.exports={parseDelimiters:E,parse:function(e,t,r,n){!function(e,t){for(var r=!1,n=0;n<e.length;n++){var o=e[n];r=P(o,r),"content"===o.type&&(o.position=r?"insidetag":"outsidetag"),"text"!==t&&C(o)&&(o.value=o.value.replace(/>/g,"&gt;"))}}(e,n);for(var o=E(e.filter(C),t,r),i=o.parsed,a=o.errors,s=[],l=0,u=0,p=0;p<e.length;p++){var c=e[p];if(C(c)){for(var f=0,h=i[l];f<h.length;f++){var d=h[f];"content"===d.type&&(d.position="insidetag"),d.lIndex=u++}w(s,i[l]),l++}else c.lIndex=u++,s.push(c)}return{errors:a,lexed:s}},xmlparse:function(e,t){var r=function(e,t,r){for(var n=0,o=e.length,i={},a=0;a<t.length;a++)i[t[a]]=!0;for(var s=0;s<r.length;s++)i[r[s]]=!1;for(var l=[];n<o&&-1!==(n=e.indexOf("<",n));){var u=n,p=e.indexOf("<",n+1);(-1===(n=e.indexOf(">",n))||-1!==p&&n>p)&&v(e,u);var c=e.slice(u,n+1),f=O(c),h=f.tag,d=f.position,g=i[h];null!=g&&l.push({type:"tag",position:d,text:g,offset:u,value:c,tag:h})}return l}(e,t.text,t.other),n=0,o=r.reduce((function(t,r){return e.length>n&&r.offset-n>0&&t.push({type:"content",value:e.substr(n,r.offset-n)}),n=r.offset+r.value.length,delete r.offset,t.push(r),t}),[]);return e.length>n&&o.push({type:"content",value:e.substr(n)}),o}}},271:function(e,t,r){var n=r(885),o=r(522),i=r(60),a=r(201),s=r(307);e.exports={docx:function(){return{getTemplatedFiles:function(){return[]},textPath:function(e){return e.textTarget},tagsXmlTextArray:["Company","HyperlinkBase","Manager","cp:category","cp:keywords","dc:creator","dc:description","dc:subject","dc:title","cp:contentStatus","w:t","a:t","m:t","vt:lpstr","vt:lpwstr"],tagsXmlLexedArray:["w:proofState","w:tc","w:tr","w:tbl","w:body","w:document","w:p","w:r","w:br","w:rPr","w:pPr","w:spacing","w:sdtContent","w:sdt","w:drawing","w:sectPr","w:type","w:headerReference","w:footerReference","w:bookmarkStart","w:bookmarkEnd","w:commentRangeStart","w:commentRangeEnd","w:commentReference"],droppedTagsInsidePlaceholder:["w:p","w:br","w:bookmarkStart","w:bookmarkEnd"],expandTags:[{contains:"w:tc",expand:"w:tr"}],onParagraphLoop:[{contains:"w:p",expand:"w:p",onlyTextInTag:!0}],tagRawXml:"w:p",baseModules:[n,o,a,i,s],tagShouldContain:[{tag:"w:sdtContent",shouldContain:["w:p","w:r","w:commentRangeStart","w:sdt"],value:"<w:p></w:p>"},{tag:"w:tc",shouldContain:["w:p"],value:"<w:p></w:p>"},{tag:"w:tr",shouldContain:["w:tc"],drop:!0},{tag:"w:tbl",shouldContain:["w:tr"],drop:!0}]}},pptx:function(){return{getTemplatedFiles:function(){return[]},textPath:function(e){return e.textTarget},tagsXmlTextArray:["Company","HyperlinkBase","Manager","cp:category","cp:keywords","dc:creator","dc:description","dc:subject","dc:title","a:t","m:t","vt:lpstr","vt:lpwstr"],tagsXmlLexedArray:["p:sp","a:tc","a:tr","a:tbl","a:graphicData","a:p","a:r","a:rPr","p:txBody","a:txBody","a:off","a:ext","p:graphicFrame","p:xfrm","a16:rowId","a:endParaRPr"],droppedTagsInsidePlaceholder:["a:p","a:endParaRPr"],expandTags:[{contains:"a:tc",expand:"a:tr"}],onParagraphLoop:[{contains:"a:p",expand:"a:p",onlyTextInTag:!0}],tagRawXml:"p:sp",baseModules:[n,a,i,s],tagShouldContain:[{tag:"a:tbl",shouldContain:["a:tr"],dropParent:"p:graphicFrame"},{tag:"p:txBody",shouldContain:["a:p"],value:"<a:p></a:p>"},{tag:"a:txBody",shouldContain:["a:p"],value:"<a:p></a:p>"}]}}}},307:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var a=r(899),s=r(946),l=s.getScopeCompilationError,u=s.getCorruptCharactersException,p=r(207),c=p.utf8ToWord,f=p.hasCorruptCharacters,h=p.removeCorruptCharacters,d=r(356),v=d.settingsContentType,g=d.coreContentType,m=d.appContentType,y=d.customContentType,b={docx:"w",pptx:"a"},x=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="Render",this.recordRun=!1,this.recordedRun=[]},t=[{key:"optionsTransformer",value:function(e,t){return this.parser=t.parser,this.fileType=t.fileType,e}},{key:"set",value:function(e){e.compiled&&(this.compiled=e.compiled),null!=e.data&&(this.data=e.data)}},{key:"getRenderedMap",value:function(e){var t=this;return Object.keys(this.compiled).reduce((function(e,r){return e[r]={from:r,data:t.data},e}),e)}},{key:"postparse",value:function(e,t){for(var r=[],n=0;n<e.length;n++){var o=e[n];if("placeholder"===o.type){var i=o.value;try{t.cachedParsers[o.lIndex]=this.parser(i,{tag:o})}catch(e){r.push(l({tag:i,rootError:e,offset:o.offset}))}}}return{postparsed:e,errors:r}}},{key:"render",value:function(e,t){var r=t.contentType,n=t.scopeManager,o=t.linebreaks,i=t.nullGetter,a=t.fileType,s=t.stripInvalidXMLChars;if(o&&-1!==[v,g,m,y].indexOf(r)&&(o=!1),o&&this.recordRuns(e),"placeholder"===e.type&&!e.module){var l;try{l=n.getValue(e.value,{part:e})}catch(e){return{errors:[e]}}if(null!=l||(l=i(e)),"string"==typeof l)if(s)l=h(l);else if(-1!==["docx","pptx","xlsx"].indexOf(a)&&f(l))return{errors:[u({tag:e.value,value:l,offset:e.offset})]};return"text"===a?{value:l}:{value:o&&"string"==typeof l?this.renderLineBreaks(l):c(l)}}}},{key:"recordRuns",value:function(e){e.tag==="".concat(b[this.fileType],":r")?this.recordedRun=[]:e.tag==="".concat(b[this.fileType],":rPr")?("start"===e.position&&(this.recordRun=!0,this.recordedRun=[e.value]),"end"!==e.position&&"selfclosing"!==e.position||(this.recordedRun.push(e.value),this.recordRun=!1)):this.recordRun&&this.recordedRun.push(e.value)}},{key:"renderLineBreaks",value:function(e){var t=this,r=b[this.fileType],n="docx"===this.fileType?"<w:r><w:br/></w:r>":"<a:br/>",o=e.split("\n"),i=this.recordedRun.join("");return o.map((function(e){return c(e)})).reduce((function(e,a,s){return e.push(a),s<o.length-1&&e.push("</".concat(r,":t></").concat(r,":r>").concat(n,"<").concat(r,":r>").concat(i,"<").concat(r,":t").concat("docx"===t.fileType?' xml:space="preserve"':"",">")),e}),[])}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return a(new x)}},320:function(e){e.exports={last:function(e){return e[e.length-1]},first:function(e){return e[0]}}},322:function(e){var t=["application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml","application/vnd.ms-word.document.macroEnabled.main+xml","application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml","application/vnd.ms-word.template.macroEnabledTemplate.main+xml"],r={main:t,docx:["application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml"].concat(t,["application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml","application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml","application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml"]),pptx:["application/vnd.openxmlformats-officedocument.presentationml.slide+xml","application/vnd.openxmlformats-officedocument.presentationml.slideMaster+xml","application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml","application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml"],xlsx:["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml","application/vnd.ms-excel.sheet.macroEnabled.main+xml","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"]};e.exports=r},356:function(e){e.exports={settingsContentType:"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml",coreContentType:"application/vnd.openxmlformats-package.core-properties+xml",appContentType:"application/vnd.openxmlformats-officedocument.extended-properties+xml",customContentType:"application/vnd.openxmlformats-officedocument.custom-properties+xml",diagramDataContentType:"application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml",diagramDrawingContentType:"application/vnd.ms-office.drawingml.diagramDrawing+xml"}},367:function(e,t,r){var n=r(207).pregMatchAll;e.exports=function(e,t){var r={content:e},o=t.join("|"),i=new RegExp("(?:(<(?:".concat(o,")[^>]*>)([^<>]*)</(?:").concat(o,")>)|(<(?:").concat(o,")[^>]*/>)"),"g");return r.matches=n(i,r.content),r}},438:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var a=r(207).pushArray,s=r(899),l=r(322),u=r(356),p=[u.settingsContentType,u.coreContentType,u.appContentType,u.customContentType,u.diagramDataContentType,u.diagramDrawingContentType],c=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="Common"},(t=[{key:"getFileType",value:function(e){var t=e.doc,r=t.invertedContentTypes;if(r){for(var n=0;n<p.length;n++){var o=p[n];r[o]&&a(t.targets,r[o])}for(var i,s=["docx","pptx","xlsx"],u=0;u<s.length;u++){for(var c=s[u],f=l[c],h=0;h<f.length;h++){var d=f[h];if(r[d])for(var v=0,g=r[d];v<g.length;v++){var m=g[v];t.relsTypes[m]&&-1===["http://purl.oclc.org/ooxml/officeDocument/relationships/officeDocument","http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument"].indexOf(t.relsTypes[m])||(i=c,-1===l.main.indexOf(d)&&d!==l.pptx[0]||t.textTarget||(t.textTarget=m),"xlsx"!==i&&t.targets.push(m))}}if(i)return i}return i}}}])&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return s(new c)}},460:function(e,t,r){var n=r(207).pushArray;function o(e,t){return t instanceof Error?n(Object.getOwnPropertyNames(t),["stack"]).reduce((function(e,r){return e[r]=t[r],"stack"===r&&(e[r]=t[r].toString()),e}),{}):t}e.exports=function(e,t){if(console.log(JSON.stringify({error:e},o,"json"===t?2:null)),e.properties&&e.properties.errors instanceof Array){var r=e.properties.errors.map((function(e){return e.properties.explanation})).join("\n");console.log("errorMessages",r)}}},522:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var a=r(899),s=r(207),l=s.isTextStart,u=s.isTextEnd,p=s.endsWith,c=s.startsWith,f=s.pushArray;function h(e,t){var r=e[t].value;return"</w:t>"===e[t+1].value||-1!==r.indexOf('xml:space="preserve"')?r:r.substr(0,r.length-1)+' xml:space="preserve">'}var d=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="SpacePreserveModule"},(t=[{key:"postparse",value:function(e,t){var r=[],n=!1,o=0,i=0,a=e.reduce((function(e,a){return function(e){return l(e)&&"w:t"===e.tag}(a)&&(n=!0,i=r.length),n?(r.push(a),function(e,t){return e&&e.basePart&&t.length>1}(t,r)&&(o=t.basePart.endLindex,r[0].value=h(r,0)),function(e,t){return"placeholder"===e.type&&t.length>1}(a,r)&&(r[i].value=h(r,i),o=a.endLindex),u(a)&&a.lIndex>o&&(0!==o&&(r[i].value=h(r,i)),f(e,r),r=[],n=!1,o=0,i=0),e):(e.push(a),e)}),[]);return f(a,r),a}},{key:"postrender",value:function(e){for(var t="",r=0,n=0,o=e.length;n<o;n++){var i=e[n];""!==i&&(p(t,'<w:t xml:space="preserve">')&&c(i,"</w:t>")&&(e[r]=t.substr(0,t.length-26)+"<w:t/>",i=i.substr(6)),t=i,r=n,e[n]=i)}return e}}])&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return a(new d)}},536:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||i(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){if(e){if("string"==typeof e)return a(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a(e,t):void 0}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var p=r(207),c=p.getRightOrNull,f=p.getRight,h=p.getLeft,d=p.getLeftOrNull,v=p.chunkBy,g=p.isTagStart,m=p.isTagEnd,y=p.isContent,b=p.last,x=p.first,w=r(946),T=w.XTTemplateError,P=w.throwExpandNotFound,O=w.getLoopPositionProducesInvalidXMLError;function S(e,t){return 0!==e.length&&0===b(e).substr(1).indexOf(t)}function j(e,t,r,n){var o=e.expandTo||n.expandTo;if(o){var i,a;try{a=h(r,o,t),i=f(r,o,t)}catch(i){var s=l({part:e,rootError:i,postparsed:r,expandTo:o,index:t},n.error);if(n.onError&&"ignore"===n.onError(s))return;P(s)}return[a,i]}}function E(e,t,r,n){var i=o(e,2),a=i[0],s=i[1],l=r.indexOf(t),u=r.slice(a,l),p=r.slice(l+1,s+1),c=n.getInner({postparse:n.postparse,index:l,part:t,leftParts:u,rightParts:p,left:a,right:s,postparsed:r});return c.length||(c.expanded=[u,p],c=[c]),{left:a,right:s,inner:c}}e.exports={expandToOne:function(e,t){var r,n=[];e.errors&&(n=e.errors,e=e.postparsed);for(var s=[],u=0,p=e.length;u<p;u++){var c=e[u];if("placeholder"===c.type&&c.module===t.moduleName&&!c.subparsed&&!c.expanded)try{var f=j(c,u,e,t);if(!f)continue;var h=o(f,2),d=h[0],v=h[1];s.push({left:d,right:v,part:c,i:u,leftPart:e[d],rightPart:e[v]})}catch(e){n.push(e)}}s.sort((function(e,t){return e.left===t.left?t.part.lIndex<e.part.lIndex?1:-1:t.left<e.left?1:-1}));for(var g=-1,m=0,y=0,b=s.length;y<b;y++){var x,w=s[y];if(g=Math.max(g,y>0?s[y-1].right:0),!(w.left<g)){var P=void 0;try{P=E([w.left+m,w.right+m],w.part,e,t)}catch(r){if(t.onError&&"ignore"===t.onError(l({part:w.part,rootError:r,postparsed:e,expandOne:E},t.errors)))continue;if(!(r instanceof T))throw r;n.push(r)}P&&(m+=P.inner.length-(P.right+1-P.left),(x=e).splice.apply(x,[P.left,P.right+1-P.left].concat(function(e){if(Array.isArray(e))return a(e)}(r=P.inner)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(r)||i(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())))}}return{postparsed:e,errors:n}},getExpandToDefault:function(e,t,r){var n=function(e){for(var t=[],r=0;r<e.length;r++){var n=e[r],o=n.position,i=n.value,a=n.tag;a&&("end"===o?S(t,a)?t.pop():t.push(i):"start"===o&&t.push(i))}return t}(e.slice(t[0].offset,t[1].offset));if(n.filter((function(e){return"/"===e[1]})).length!==n.filter((function(e){return"/"!==e[1]&&"/"!==e[e.length-2]})).length)return{error:O({tag:x(t).part.value,offset:[x(t).part.offset,b(t).part.offset]})};for(var o,i=function(){var o=r[a],i=o.contains,s=o.expand,l=o.onlyTextInTag;if(function(e,t){for(var r=0;r<t.length;r++)if(0===t[r].indexOf("<".concat(e)))return!0;return!1}(i,n)){if(l){var u=d(e,i,t[0].offset),p=c(e,i,t[1].offset);if(null===u||null===p)return 0;var f=v(e.slice(u,p),(function(e){return g(i,e)?"start":m(i,e)?"end":null})),h=x(f),w=b(f),T=h.filter(y),P=w.filter(y);if(1!==T.length||1!==P.length)return 0}return{v:{value:s}}}},a=0;a<r.length;a++)if(0!==(o=i())&&o)return o.v;return{}}}},650:function(e){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}var r=new RegExp(String.fromCharCode(160),"g");function n(e){return e.replace(r," ")}e.exports={match:function(e,r){var o=t(e);return"string"===o?n(r.substr(0,e.length))===e:e instanceof RegExp?e.test(n(r)):"function"===o?!!e(r):void 0},getValue:function(e,r){var o=t(e);return"string"===o?n(r).substr(e.length):e instanceof RegExp?n(r).match(e)[1]:"function"===o?e(r):void 0},getValues:function(e,r){var o=t(e);return"string"===o?[r,n(r).substr(e.length)]:e instanceof RegExp?n(r).match(e):"function"===o?[r,e(r)]:void 0}}},673:function(e){e.exports={XMLSerializer:window.XMLSerializer,DOMParser:window.DOMParser,XMLDocument:window.XMLDocument}},690:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return l(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var u=r(207),p=u.wordToUtf8,c=u.pushArray,f=r(650),h=f.match,d=f.getValue,v=f.getValues;var g={preparse:function(e,t,r){return function(e,r){for(var n=0;n<t.length;n++)e=t[n].preparse(e,r)||e;return e}(e,r)},parse:function(e,t,r){var n,o=!1,a="",l=[],u=r.fileTypeConfig.droppedTagsInsidePlaceholder||[];return e.reduce((function(e,f){return"delimiter"===f.type?(o="start"===f.position,"end"===f.position&&(r.parse=function(e){return function(e,t){var r,n=t.modules,o=t.startOffset,a=t.lIndex;t.offset=o,t.match=h,t.getValue=d,t.getValues=v;var l=function(e,t){for(var r=[],n=0;n<e.length;n++){var o=e[n];if(o.matchers){var i=o.matchers(t);if(!(i instanceof Array))throw new Error("module matcher returns a non array");c(r,i)}}return r}(n,t),u=function(e,t,r){for(var n=[],o=0;o<e.length;o++){var a=e[o],l=s(a,2),u=l[0],p=l[1],c=a[2]||{};if(r.match(u,t)){var f=r.getValues(u,t);if("function"==typeof c&&(c=c(f)),!c.value){var h=s(f,2);c.value=h[1]}n.push(i({type:"placeholder",prefix:u,module:p,onMatch:c.onMatch,priority:c.priority},c))}}return n}(l,e,t);if(u.length>0){for(var p=null,f=0;f<u.length;f++){var g=u[f];g.priority||(g.priority=-g.value.length),(!p||g.priority>p.priority)&&(p=g)}return p.offset=o,delete p.priority,p.endLindex=a,p.lIndex=a,p.raw=e,p.onMatch&&p.onMatch(p),delete p.onMatch,delete p.prefix,p}for(var m=0;m<n.length;m++)if(r=n[m].parse(e,t))return r.offset=o,r.endLindex=a,r.lIndex=a,r.raw=e,r;return{type:"placeholder",value:e,offset:o,endLindex:a,lIndex:a}}(e,i(i(i({},r),f),{},{startOffset:n,modules:t}))},e.push(r.parse(p(a))),c(e,l),l=[]),"start"===f.position&&(l=[],n=f.offset),a="",e):o?"content"!==f.type||"insidetag"!==f.position?(-1!==u.indexOf(f.tag)||l.push(f),e):(a+=f.value,e):(e.push(f),e)}),[])},postparse:function(e,t,r){function n(e,r){return t.map((function(t){return t.getTraits(e,r)}))}var o=[];return{postparsed:function e(r,a){return t.reduce((function(t,r){var s=r.postparse(t,i(i({},a),{},{postparse:function(t,r){return e(t,i(i({},a),r))},getTraits:n}));return null==s?t:s.errors?(c(o,s.errors),s.postparsed):s}),r)}(e,r),errors:o}}};e.exports=g},779:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var a=r(946).getScopeParserExecutionError,s=r(320).last,l=r(207).concatArrays;function u(e,t){for(var r,n=e.length>>>0,o=0;o<n;o++)if(r=e[o],t.call(this,r,o,e))return r}function p(e,t,r){var n,o,i=this,s=this.scopeList[r];if(this.root.finishedResolving){for(var l=this.resolved,c=function(){var e=i.scopeLindex[f];l=(l=u(l,(function(t){return t.lIndex===e}))).value[i.scopePathItem[f]]},f=this.resolveOffset,h=this.scopePath.length;f<h;f++)c();return u(l,(function(e){return t.part.lIndex===e.lIndex})).value}o=this.cachedParsers&&t.part?this.cachedParsers[t.part.lIndex]?this.cachedParsers[t.part.lIndex]:this.cachedParsers[t.part.lIndex]=this.parser(e,{tag:t.part,scopePath:this.scopePath}):this.parser(e,{tag:t.part,scopePath:this.scopePath});try{n=o.get(s,this.getContext(t,r))}catch(r){throw a({tag:e,scope:s,error:r,offset:t.part.offset})}return null==n&&r>0?p.call(this,e,t,r-1):n}function c(e,t,r){var n,o=this,i=this.scopeList[r];return n=this.cachedParsers&&t.part?this.cachedParsers[t.part.lIndex]?this.cachedParsers[t.part.lIndex]:this.cachedParsers[t.part.lIndex]=this.parser(e,{tag:t.part,scopePath:this.scopePath}):this.parser(e,{tag:t.part,scopePath:this.scopePath}),Promise.resolve().then((function(){return n.get(i,o.getContext(t,r))})).catch((function(r){throw a({tag:e,scope:i,error:r,offset:t.part.offset})})).then((function(n){return null==n&&r>0?c.call(o,e,t,r-1):n}))}var f=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.root=t.root||this,this.resolveOffset=t.resolveOffset||0,this.scopePath=t.scopePath,this.scopePathItem=t.scopePathItem,this.scopePathLength=t.scopePathLength,this.scopeList=t.scopeList,this.scopeType="",this.scopeTypes=t.scopeTypes,this.scopeLindex=t.scopeLindex,this.parser=t.parser,this.resolved=t.resolved,this.cachedParsers=t.cachedParsers}return t=e,(r=[{key:"loopOver",value:function(e,t,r,n){return this.loopOverValue(this.getValue(e,n),t,r)}},{key:"functorIfInverted",value:function(e,t,r,n,o){return e&&t(r,n,o),e}},{key:"isValueFalsy",value:function(e,t){return null==e||!e||"[object Array]"===t&&0===e.length}},{key:"loopOverValue",value:function(e,t,r){this.root.finishedResolving&&(r=!1);var n=Object.prototype.toString.call(e);if(this.isValueFalsy(e,n))return this.scopeType=!1,this.functorIfInverted(r,t,s(this.scopeList),0,1);if("[object Array]"===n){this.scopeType="array";for(var o=0;o<e.length;o++)this.functorIfInverted(!r,t,e[o],o,e.length);return!0}return"[object Object]"===n?(this.scopeType="object",this.functorIfInverted(!r,t,e,0,1)):this.functorIfInverted(!r,t,s(this.scopeList),0,1)}},{key:"getValue",value:function(e,t){var r=p.call(this,e,t,this.scopeList.length-1);return"function"==typeof r?r(this.scopeList[this.scopeList.length-1],this):r}},{key:"getValueAsync",value:function(e,t){var r=this;return c.call(this,e,t,this.scopeList.length-1).then((function(e){return"function"==typeof e?e(r.scopeList[r.scopeList.length-1],r):e}))}},{key:"getContext",value:function(e,t){return{num:t,meta:e,scopeList:this.scopeList,resolved:this.resolved,scopePath:this.scopePath,scopeTypes:this.scopeTypes,scopePathItem:this.scopePathItem,scopePathLength:this.scopePathLength}}},{key:"createSubScopeManager",value:function(t,r,n,o,i){return new e({root:this.root,resolveOffset:this.resolveOffset,resolved:this.resolved,parser:this.parser,cachedParsers:this.cachedParsers,scopeTypes:l([this.scopeTypes,[this.scopeType]]),scopeList:l([this.scopeList,[t]]),scopePath:l([this.scopePath,[r]]),scopePathItem:l([this.scopePathItem,[n]]),scopePathLength:l([this.scopePathLength,[i]]),scopeLindex:l([this.scopeLindex,[o.lIndex]])})}}])&&o(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();e.exports=function(e){return e.scopePath=[],e.scopePathItem=[],e.scopePathLength=[],e.scopeTypes=[],e.scopeLindex=[],e.scopeList=[e.tags],new f(e)}},789:function(e,t,r){var n=r(946),o=n.throwUnimplementedTagType,i=n.XTScopeParserError,a=r(207).pushArray,s=r(830);e.exports=function(e){var t=e.baseNullGetter,r=e.compiled,n=e.scopeManager;e.nullGetter=function(e,r){return t(e,r||n)};var l=[],u=r.map((function(t,r){var n;e.index=r,e.resolvedId=s(t,e);try{n=function(e,t){for(var r=0,n=t.modules;r<n.length;r++){var o=n[r].render(e,t);if(o)return o}return!1}(t,e)}catch(e){if(e instanceof i)return l.push(e),t;throw e}return n?(n.errors&&a(l,n.errors),n):"content"===t.type||"tag"===t.type?t:void o(t,r)})).reduce((function(e,t){var r=t.value;return r instanceof Array?a(e,r):r&&e.push(r),e}),[]);return{errors:l,parts:u}}},798:function(e){function t(e,t){for(var r=-1,n=0,o=e.length;n<o;n++)t[n]>=e[n].length||(-1===r||e[n][t[n]].offset<e[r][t[r]].offset)&&(r=n);return r}e.exports=function(e){var r=e.reduce((function(e,t){return e+t.length}),0);e=e.filter((function(e){return e.length>0}));for(var n=new Array(r),o=e.map((function(){return 0})),i=0;i<r;i++){var a=t(e,o);n[i]=e[a][o[a]],o[a]++}return n}},807:function(e,t,r){var n=["modules"];function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(e,t,r){return(t=c(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return l(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function p(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,c(n.key),n)}}function c(e){var t=function(e){if("object"!=u(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==u(t)?t:t+""}var f=r(207);f.traits=r(536),f.moduleWrapper=r(899);var h=r(438),d=r(779),v=r(263),g=r(903).getTags,m=r(460),y=r(891),b=r(946),x=b.throwMultiError,w=b.throwResolveBeforeCompile,T=b.throwRenderInvalidTemplate,P=b.throwRenderTwice,O=b.XTInternalError,S=b.XTTemplateError,j=b.throwFileTypeNotIdentified,E=b.throwFileTypeNotHandled,C=b.throwApiVersionError,k=f.getDefaults,I=f.str2xml,A=f.xml2str,M=f.moduleWrapper,D=f.concatArrays,L=f.uniq,_=f.getDuplicates,R=f.stableSort,F=f.pushArray,X="[Content_Types].xml",V="_rels/.rels",N=[3,46,0];function U(e){var t=[];for(var r in e)t.push(r);for(var n=[X,V],o=["word/","xl/","ppt/"],i=0;i<t.length;i++)for(var a=t[i],s=0;s<o.length;s++){var l=o[s];0===a.indexOf("".concat(l))&&n.push(a)}for(var u=0;u<t.length;u++){var p=t[u];-1===n.indexOf(p)&&n.push(p)}return n}function z(e,t){!0!==e.hideDeprecations&&console.warn(t)}function B(e,t){if(!0!==e.hideDeprecations)return z(e,'Deprecated method ".'.concat(t,'", view upgrade guide : https://docxtemplater.com/docs/api/#upgrade-guide, stack : ').concat((new Error).stack))}function W(e){e.modules=e.modules.filter((function(t){if(!t.supportedFileTypes)return!0;if(!Array.isArray(t.supportedFileTypes))throw new Error("The supportedFileTypes field of the module must be an array");var r=t.supportedFileTypes.includes(e.fileType);return r||t.on("detached"),r}))}function Z(e){var t=e.compiled;e.errors=D(Object.keys(t).map((function(e){return t[e].allErrors}))),0!==e.errors.length&&(e.options.errorLogging&&m(e.errors,e.options.errorLogging),x(e.errors))}var G=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.modules,i=void 0===o?[]:o,a=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(r,n);if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.targets=[],this.rendered=!1,this.scopeManagers={},this.compiled={},this.modules=[h()],this.xmlDocuments={},0===arguments.length)z(this,"Deprecated docxtemplater constructor with no arguments, view upgrade guide : https://docxtemplater.com/docs/api/#upgrade-guide, stack : ".concat((new Error).stack)),this.hideDeprecations=!0,this.setOptions(a);else{if(this.hideDeprecations=!0,this.setOptions(a),!t||!t.files||"function"!=typeof t.file)throw new Error("The first argument of docxtemplater's constructor must be a valid zip file (jszip v2 or pizzip v3)");if(!Array.isArray(i))throw new Error("The modules argument of docxtemplater's constructor must be an array");for(var s=0;s<i.length;s++){var l=i[s];this.attachModule(l)}this.loadZip(t),this.compile(),this.v4Constructor=!0}this.hideDeprecations=!1}return t=e,r=[{key:"verifyApiVersion",value:function(e){return 3!==(e=e.split(".").map((function(e){return parseInt(e,10)}))).length&&C("neededVersion is not a valid version",{neededVersion:e,explanation:"the neededVersion must be an array of length 3"}),e[0]!==N[0]&&C("The major api version do not match, you probably have to update docxtemplater with npm install --save docxtemplater",{neededVersion:e,currentModuleApiVersion:N,explanation:"moduleAPIVersionMismatch : needed=".concat(e.join("."),", current=").concat(N.join("."))}),e[1]>N[1]&&C("The minor api version is not uptodate, you probably have to update docxtemplater with npm install --save docxtemplater",{neededVersion:e,currentModuleApiVersion:N,explanation:"moduleAPIVersionMismatch : needed=".concat(e.join("."),", current=").concat(N.join("."))}),e[1]===N[1]&&e[2]>N[2]&&C("The patch api version is not uptodate, you probably have to update docxtemplater with npm install --save docxtemplater",{neededVersion:e,currentModuleApiVersion:N,explanation:"moduleAPIVersionMismatch : needed=".concat(e.join("."),", current=").concat(N.join("."))}),!0}},{key:"setModules",value:function(e){for(var t=0,r=this.modules;t<r.length;t++)r[t].set(e)}},{key:"sendEvent",value:function(e){for(var t=0,r=this.modules;t<r.length;t++)r[t].on(e)}},{key:"attachModule",value:function(e){if(this.v4Constructor)throw new O("attachModule() should not be called manually when using the v4 constructor");B(this,"attachModule");var t=u(e);if("function"===t)throw new O("Cannot attach a class/function as a module. Most probably you forgot to instantiate the module by using `new` on the module.");if(!e||"object"!==t)throw new O("Cannot attachModule with a falsy value");if(e.requiredAPIVersion&&this.verifyApiVersion(e.requiredAPIVersion),!0===e.attached){if("function"!=typeof e.clone)throw new Error('Cannot attach a module that was already attached : "'.concat(e.name,'". The most likely cause is that you are instantiating the module at the root level, and using it for multiple instances of Docxtemplater'));e=e.clone()}e.attached=!0;var r=M(e);return this.modules.push(r),r.on("attached"),this.fileType&&W(this),this}},{key:"setOptions",value:function(e){var t,r;if(this.v4Constructor)throw new Error("setOptions() should not be called manually when using the v4 constructor");if(B(this,"setOptions"),!e)throw new Error("setOptions should be called with an object as first parameter");this.options={};var n=k();for(var o in n){var i=n[o];this.options[o]=null!=e[o]?e[o]:this[o]||i,this[o]=this.options[o]}return(t=this.delimiters).start&&(t.start=f.utf8ToWord(this.delimiters.start)),(r=this.delimiters).end&&(r.end=f.utf8ToWord(this.delimiters.end)),this}},{key:"loadZip",value:function(e){if(this.v4Constructor)throw new Error("loadZip() should not be called manually when using the v4 constructor");if(B(this,"loadZip"),e.loadAsync)throw new O("Docxtemplater doesn't handle JSZip version >=3, please use pizzip");this.zip=e,this.updateFileTypeConfig(),this.modules=D([this.fileTypeConfig.baseModules.map((function(e){return e()})),this.modules]);for(var t=0,r=this.modules;t<r.length;t++){var n=r[t];n.zip=this.zip,n.docxtemplater=this}return W(this),this}},{key:"precompileFile",value:function(e){var t=this.createTemplateClass(e);t.preparse(),this.compiled[e]=t}},{key:"compileFile",value:function(e){this.compiled[e].parse()}},{key:"getScopeManager",value:function(e,t,r){var n;return(n=this.scopeManagers)[e]||(n[e]=d({tags:r,parser:this.parser,cachedParsers:t.cachedParsers})),this.scopeManagers[e]}},{key:"resolveData",value:function(e){var t=this;B(this,"resolveData");var r=[];return Object.keys(this.compiled).length||w(),Promise.resolve(e).then((function(e){return t.data=e,t.setModules({data:t.data,Lexer:v}),t.mapper=t.modules.reduce((function(e,t){return t.getRenderedMap(e)}),{}),Promise.all(Object.keys(t.mapper).map((function(e){var n=t.mapper[e],o=n.from,i=n.data;return Promise.resolve(i).then((function(n){var i=t.compiled[o];return i.filePath=e,i.scopeManager=t.getScopeManager(e,i,n),i.resolveTags(n).then((function(e){return i.scopeManager.finishedResolving=!0,e}),(function(e){F(r,e)}))}))}))).then((function(e){return 0!==r.length&&(t.options.errorLogging&&m(r,t.options.errorLogging),x(r)),D(e)}))}))}},{key:"reorderModules",value:function(){this.modules=R(this.modules,(function(e,t){return(t.priority||0)-(e.priority||0)}))}},{key:"throwIfDuplicateModules",value:function(){var e=_(this.modules.map((function(e){return e.name})));if(e.length>0)throw new O('Detected duplicate module "'.concat(e[0],'"'))}},{key:"compile",value:function(){var e=this;if(B(this,"compile"),this.updateFileTypeConfig(),this.throwIfDuplicateModules(),this.reorderModules(),Object.keys(this.compiled).length)return this;this.options=this.modules.reduce((function(t,r){return r.optionsTransformer(t,e)}),this.options),this.options.xmlFileNames=L(this.options.xmlFileNames);for(var t=0,r=this.options.xmlFileNames;t<r.length;t++){var n=r[t],o=this.zip.files[n].asText();this.xmlDocuments[n]=I(o)}this.setModules({zip:this.zip,xmlDocuments:this.xmlDocuments}),this.getTemplatedFiles(),this.sendEvent("before-preparse");for(var i=0,a=this.templatedFiles;i<a.length;i++){var s=a[i];null!=this.zip.files[s]&&this.precompileFile(s)}this.sendEvent("after-preparse");for(var l=0,u=this.templatedFiles;l<u.length;l++){var p=u[l];null!=this.zip.files[p]&&this.compiled[p].parse({noPostParse:!0})}this.sendEvent("after-parse");for(var c=0,f=this.templatedFiles;c<f.length;c++){var h=f[c];null!=this.zip.files[h]&&this.compiled[h].postparse()}return this.sendEvent("after-postparse"),this.setModules({compiled:this.compiled}),Z(this),this}},{key:"getRelsTypes",value:function(){for(var e=this.zip.files[V],t=e?I(e.asText()):null,r=t?t.getElementsByTagName("Relationship"):[],n={},o=0;o<r.length;o++){var i=r[o];n[i.getAttribute("Target")]=i.getAttribute("Type")}return n}},{key:"getContentTypes",value:function(){var e=this.zip.files[X],t=e?I(e.asText()):null;return{overrides:t?t.getElementsByTagName("Override"):null,defaults:t?t.getElementsByTagName("Default"):null,contentTypes:e,contentTypeXml:t}}},{key:"updateFileTypeConfig",value:function(){this.relsTypes=this.getRelsTypes();var t,r=this.getContentTypes(),n=r.overrides,o=r.defaults,i=r.contentTypes,a=r.contentTypeXml;a&&(this.filesContentTypes=y(n,o,this.zip),this.invertedContentTypes=f.invertMap(this.filesContentTypes),this.setModules({contentTypes:this.contentTypes,invertedContentTypes:this.invertedContentTypes})),this.zip.files.mimetype&&(t="odt");for(var s=0,l=this.modules;s<l.length;s++)t=l[s].getFileType({zip:this.zip,contentTypes:i,contentTypeXml:a,overrides:n,defaults:o,doc:this})||t;"odt"===t&&E(t),t||j(this.zip);for(var u=0,p=this.modules;u<p.length;u++)for(var c=0,h=p[u].xmlContentTypes||[];c<h.length;c++){var d=h[c];F(this.options.xmlFileNames,this.invertedContentTypes[d]||[])}if(this.fileType=t,W(this),this.fileTypeConfig=this.options.fileTypeConfig||this.fileTypeConfig,!this.fileTypeConfig){if(!e.FileTypeConfig[this.fileType]){var v='Filetype "'.concat(this.fileType,'" is not supported'),g="filetype_not_supported";"xlsx"===this.fileType&&(v='Filetype "'.concat(this.fileType,'" is supported only with the paid XlsxModule'),g="xlsx_filetype_needs_xlsx_module");var m=new S(v);throw m.properties={id:g,explanation:v},m}this.fileTypeConfig=e.FileTypeConfig[this.fileType]()}return this}},{key:"renderAsync",value:function(e){var t=this;this.hideDeprecations=!0;var r=this.resolveData(e);return this.hideDeprecations=!1,r.then((function(){return t.render()}))}},{key:"render",value:function(e){this.rendered&&P(),this.rendered=!0,0===Object.keys(this.compiled).length&&this.compile(),this.errors.length>0&&T(),arguments.length>0&&(this.data=e),this.setModules({data:this.data,Lexer:v}),this.mapper||(this.mapper=this.modules.reduce((function(e,t){return t.getRenderedMap(e)}),{}));var t=[];for(var r in this.mapper){var n=this.mapper[r],o=n.from,i=n.data,a=this.compiled[o];a.scopeManager=this.getScopeManager(r,a,i),a.render(r),t.push([r,a.content,a]),delete a.content}for(var l=0;l<t.length;l++)for(var u=t[l],p=s(u,3),c=p[1],f=p[2],h=0,d=this.modules;h<d.length;h++){var g=d[h];if(g.preZip){var m=g.preZip(c,f);"string"==typeof m&&(u[1]=m)}}for(var y=0;y<t.length;y++){var b=s(t[y],2),x=b[0],w=b[1];this.zip.file(x,w,{createFolders:!0})}return Z(this),this.sendEvent("syncing-zip"),this.syncZip(),this.sendEvent("synced-zip"),this}},{key:"syncZip",value:function(){for(var e in this.xmlDocuments){this.zip.remove(e);var t=A(this.xmlDocuments[e]);this.zip.file(e,t,{createFolders:!0})}}},{key:"setData",value:function(e){return B(this,"setData"),this.data=e,this}},{key:"getZip",value:function(){return this.zip}},{key:"createTemplateClass",value:function(e){var t=this.zip.files[e].asText();return this.createTemplateClassFromContent(t,e)}},{key:"createTemplateClassFromContent",value:function(t,r){for(var n={filePath:r,contentType:this.filesContentTypes[r],relsType:this.relsTypes[r]},o=k(),i=F(Object.keys(o),["filesContentTypes","fileTypeConfig","fileType","modules"]),a=0;a<i.length;a++){var s=i[a];n[s]=this[s]}return new e.XmlTemplater(t,n)}},{key:"getFullText",value:function(e){return this.createTemplateClass(e||this.fileTypeConfig.textPath(this)).getFullText()}},{key:"getTemplatedFiles",value:function(){return this.templatedFiles=this.fileTypeConfig.getTemplatedFiles(this.zip),F(this.templatedFiles,this.targets),this.templatedFiles=L(this.templatedFiles),this.templatedFiles}},{key:"getTags",value:function(){var e={headers:[],footers:[]};for(var t in this.compiled){var r=this.filesContentTypes[t];"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"===r&&(e.document={target:t,tags:g(this.compiled[t].postparsed)}),"application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml"===r&&e.headers.push({target:t,tags:g(this.compiled[t].postparsed)}),"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml"===r&&e.footers.push({target:t,tags:g(this.compiled[t].postparsed)})}return e}},{key:"toBuffer",value:function(e){return this.getZip().generate(i(i({compression:"DEFLATE",fileOrder:U},e),{},{type:"nodebuffer"}))}},{key:"toBlob",value:function(e){return this.getZip().generate(i(i({compression:"DEFLATE",fileOrder:U},e),{},{type:"blob"}))}},{key:"toBase64",value:function(e){return this.getZip().generate(i(i({compression:"DEFLATE",fileOrder:U},e),{},{type:"base64"}))}},{key:"toUint8Array",value:function(e){return this.getZip().generate(i(i({compression:"DEFLATE",fileOrder:U},e),{},{type:"uint8array"}))}},{key:"toArrayBuffer",value:function(e){return this.getZip().generate(i(i({compression:"DEFLATE",fileOrder:U},e),{},{type:"arraybuffer"}))}}],r&&p(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();G.DocUtils=f,G.Errors=r(946),G.XmlTemplater=r(245),G.FileTypeConfig=r(271),G.XmlMatcher=r(367),e.exports=G,e.exports.default=G},830:function(e){e.exports=function(e,t){if(null==e.lIndex)return null;var r=t.scopeManager.scopePathItem;return e.parentPart&&(r=r.slice(0,r.length-1)),t.filePath+"@"+e.lIndex.toString()+"-"+r.join("-")}},885:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(e,t,r){return(t=u(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u(n.key),n)}}function u(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var p=r(207),c=p.chunkBy,f=p.last,h=p.isParagraphStart,d=p.isModule,v=p.pushArray,g=p.isParagraphEnd,m=p.isContent,y=p.startsWith,b=p.isTagEnd,x=p.isTagStart,w=p.getSingleAttribute,T=p.setSingleAttribute,P=r(322),O=r(899),S="loop";function j(e){var t=function(e){for(var t=0;t<e.length;t++){var r=e[t];if("content"!==r.type)return r}return null}(e.subparsed);return null!=t&&"w:t"!==t.tag}function E(e){return e.hasPageBreak&&j(e)?'<w:p><w:r><w:br w:type="page"/></w:r></w:p>':""}function C(e){return e.some((function(e){return m(e)}))?0:e.length}function k(e){var t=e.parts.length-1;"</w:p>"===e.parts[t]?e.parts.splice(t,0,'<w:r><w:br w:type="page"/></w:r>'):e.parts.push('<w:p><w:r><w:br w:type="page"/></w:r></w:p>')}function I(e){return e.some((function(e){return"w:br"===e.tag&&-1!==e.value.indexOf('w:type="page"')}))}function A(e){return e.some((function(e){return"w:drawing"===e.tag}))}var M=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="LoopModule",this.inXfrm=!1,this.totalSectPr=0,this.prefix={start:"#",end:"/",dash:/^-([^\s]+)\s(.+)/,inverted:"^"}},t=[{key:"optionsTransformer",value:function(e,t){return this.docxtemplater=t,e}},{key:"preparse",value:function(e,t){var r=t.contentType;-1!==P.main.indexOf(r)&&(this.sects=function(e){for(var t=!1,r=[],n=0;n<e.length;n++){var o=e[n];x("w:sectPr",o)&&(r.push([]),t=!0),t&&r[r.length-1].push(o),b("w:sectPr",o)&&(t=!1)}return r}(e))}},{key:"matchers",value:function(){var e=S;return[[this.prefix.start,e,{expandTo:"auto",location:"start",inverted:!1}],[this.prefix.inverted,e,{expandTo:"auto",location:"start",inverted:!0}],[this.prefix.end,e,{location:"end"}],[this.prefix.dash,e,function(e){var t=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(e,3);return{location:"start",inverted:!1,expandTo:t[1],value:t[2]}}]]}},{key:"getTraits",value:function(e,t){if("expandPair"===e)return t.reduce((function(e,t,r){return d(t,S)&&null==t.subparsed&&e.push({part:t,offset:r}),e}),[])}},{key:"postparse",value:function(e,t){var r=t.basePart;if(r&&"docx"===this.docxtemplater.fileType&&e.length>0){r.sectPrCount=function(e){for(var t=!1,r=0,n=0;n<e.length;n++){var o=e[n];x("w:sectPr",o)&&(t=!0),t&&("w:headerReference"!==o.tag&&"w:footerReference"!==o.tag||(r++,t=!1)),b("w:sectPr",o)&&(t=!1)}return r}(e),this.totalSectPr+=r.sectPrCount;var n=this.sects;n.some((function(t,o){return r.lIndex<t[0].lIndex?(o+1<n.length&&n[o+1].some((function(e){return x("w:type",e)&&-1!==e.value.indexOf("continuous")}))&&(r.addContinuousType=!0),!0):e[0].lIndex<t[0].lIndex&&t[0].lIndex<r.lIndex?(n[o].some((function(e){return x("w:type",e)&&-1!==e.value.indexOf('w:val="nextPage"')}))&&(r.addNextPage={index:o}),!0):void 0})),r.lastParagrapSectPr=function(e){for(var t=[],r=!1,n=e.length-1;n>=0;n--){var o=e[n];if(b("w:sectPr",o)&&(r=!0),x("w:sectPr",o)&&(t.unshift(o.value),r=!1),r&&t.unshift(o.value),h(o)){if(t.length>0)return t.join("");break}}return""}(e)}if(!r||"auto"!==r.expandTo||r.module!==S||!function(e){return e.length&&h(e[0])&&g(f(e))}(e))return e;r.paragraphLoop=!0;var o=0,i=c(e,(function(e){return h(e)&&1==++o?"start":g(e)&&0==--o?"end":null})),a=i[0],s=f(i),l=C(a),u=C(s);return r.hasPageBreakBeginning=I(a),r.hasPageBreak=I(s),A(a)&&(l=0),A(s)&&(u=0),e.slice(l,e.length-u)}},{key:"resolve",value:function(e,t){if(!d(e,S))return null;var r=t.scopeManager,n=r.getValueAsync(e.value,{part:e}),o=[];function a(n,a,s){var l=r.createSubScopeManager(n,e.value,a,e,s);o.push(t.resolve(i(i({},t),{},{compiled:e.subparsed,tags:{},scopeManager:l})))}var s=[];return n.then((function(t){return new Promise((function(e){t instanceof Array?Promise.all(t).then(e):e(t)})).then((function(t){return r.loopOverValue(t,a,e.inverted),Promise.all(o).then((function(e){return e.map((function(e){var t=e.resolved,r=e.errors;return v(s,r),t}))})).then((function(e){if(s.length>0)throw s;return e}))}))}))}},{key:"render",value:function(e,t){if("p:xfrm"===e.tag&&(this.inXfrm="start"===e.position),"a:ext"===e.tag&&this.inXfrm)return this.lastExt=e,e;if(!d(e,S))return null;var r=[],n=[],o=0,a=this,s=e.subparsed[0],l=0;"a:tr"===(null==s?void 0:s.tag)&&(l=+w(s.value,"h")),o-=l;var u=0,p=j(e);if(!1===t.scopeManager.loopOver(e.value,(function(s,c,f){o+=l;for(var h=t.scopeManager.createSubScopeManager(s,e.value,c,e,f),d=0,g=e.subparsed;d<g.length;d++){var m=g[d];if(x("a16:rowId",m)){var b=+w(m.value,"val")+u;u=1,m.value=T(m.value,"val",b)}}var P,O,S,j=t.render(i(i({},t),{},{compiled:e.subparsed,tags:{},scopeManager:h}));e.hasPageBreak&&c===f-1&&p&&k(j),h.scopePathItem.some((function(e){return 0!==e}))?(1===e.sectPrCount&&(j.parts=j.parts.filter((function(e){return!y(e,"<w:headerReference")&&!y(e,"<w:footerReference")}))),e.addContinuousType&&(j.parts=(P=j.parts,O=!1,S=!1,P.reduce((function(e,t){return!1===O&&y(t,"<w:sectPr")&&(S=!0),S&&(y(t,"<w:type")&&(O=!0),!1===O&&y(t,"</w:sectPr")&&e.push('<w:type w:val="continuous"/>')),e.push(t),e}),[])))):e.addNextPage&&(j.parts=function(e,t){return v(["<w:p><w:pPr>".concat(t.map((function(e){return e.value})).join(""),"</w:pPr></w:p>")],e)}(j.parts,a.sects[e.addNextPage.index])),e.addNextPage&&k(j),e.hasPageBreakBeginning&&p&&function(e){e.parts.unshift('<w:p><w:r><w:br w:type="page"/></w:r></w:p>')}(j);for(var E=0,C=j.parts;E<C.length;E++){var I=C[E];r.push(I)}v(n,j.errors)}),e.inverted,{part:e}))return e.lastParagrapSectPr?e.paragraphLoop?{value:"<w:p><w:pPr>".concat(e.lastParagrapSectPr,"</w:pPr></w:p>")}:{value:"</w:t></w:r></w:p><w:p><w:pPr>".concat(e.lastParagrapSectPr,"</w:pPr><w:r><w:t>")}:{value:E(e)||"",errors:n};if(0!==o){var c=+w(this.lastExt.value,"cy");this.lastExt.value=T(this.lastExt.value,"cy",c+o)}return{value:t.joinUncorrupt(r,i(i({},t),{},{basePart:e})),errors:n}}}],t&&l(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return O(new M)}},891:function(e){e.exports=function(e,t,r){for(var n={},o=0;o<e.length;o++){var i=e[o],a=i.getAttribute("ContentType"),s=i.getAttribute("PartName").substr(1);n[s]=a}for(var l=function(){var e=t[u],o=e.getAttribute("ContentType"),i=e.getAttribute("Extension");r.file(/./).map((function(e){var t=e.name;t.slice(t.length-i.length)!==i||n[t]||"[Content_Types].xml"===t||(n[t]=o)}))},u=0;u<t.length;u++)l();return n}},899:function(e,t,r){var n=r(946).XTInternalError;function o(){}function i(e){return e}e.exports=function(e){var t={set:o,matchers:function(){return[]},parse:o,render:o,getTraits:o,getFileType:o,nullGetter:o,optionsTransformer:i,postrender:i,errorsTransformer:i,getRenderedMap:i,preparse:i,postparse:i,on:o,resolve:o,preResolve:o};if(Object.keys(t).every((function(t){return!e[t]}))){var r=new n("This module cannot be wrapped, because it doesn't define any of the necessary functions");throw r.properties={id:"module_cannot_be_wrapped",explanation:"This module cannot be wrapped, because it doesn't define any of the necessary functions"},r}for(var a in t)e[a]||(e[a]=t[a]);return e}},903:function(e){function t(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function n(e){return"placeholder"===e.type}e.exports={getTags:function(e){var r={},o=[{items:e.filter(n),parents:[],path:[]}];function i(e,r,n){n.length&&o.push({items:n,parents:[].concat(t(r.parents),[e]),path:!1===e.dataBound||e.attrParsed||!e.value||e.attrParsed?t(r.path):[].concat(t(r.path),[e.value])})}function a(e,t){for(var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.length,n=e,o=0;o<r;o++)n=n[t[o]];return n}function s(e,t){return t.reduce((function(t,r){return("number"==typeof r.lIndex?r.lIndex:parseInt(r.lIndex.split("-")[0],10))>e.lIndex?t-1:t}),t.length)}for(;o.length>0;)for(var l=o.pop(),u=a(r,l.path),p=0,c=l.items;p<c.length;p++){var f,h,d,v,g=c[p];if(g.attrParsed)for(var m in g.attrParsed)i(g,l,g.attrParsed[m].filter(n));else if(g.subparsed)!1!==g.dataBound&&((d=u)[v=g.value]||(d[v]={})),i(g,l,g.subparsed.filter(n));else if(g.cellParsed)for(var y=0,b=g.cellPostParsed;y<b.length;y++){var x=b[y];if("placeholder"===x.type){if("pro-xml-templating/xls-module-loop"===x.module)continue;if(x.subparsed){var w,T;(w=u)[T=x.value]||(w[T]={}),i(x,l,x.subparsed.filter(n))}else{var P,O,S=s(g,l.parents);(P=u=a(r,l.path,S))[O=x.value]||(P[O]={})}}}else!1!==g.dataBound&&((f=u)[h=g.value]||(f[h]={}))}return r},isPlaceholder:n}},945:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var s=r(207).pushArray,l=r(830);e.exports=function(e){var t=[],r=e.baseNullGetter,n=e.compiled,o=e.scopeManager;e.nullGetter=function(e,t){return r(e,t||o)},e.resolved=t;var a=[];return Promise.all(n.filter((function(e){return-1===["content","tag"].indexOf(e.type)})).reduce((function(r,n){var u,p=function(e,t){for(var r=0,n=t.modules;r<n.length;r++){var o=n[r].resolve(e,t);if(o)return o}return!1}(n,i(i({},e),{},{resolvedId:l(n,e)}));if(p)u=p.then((function(e){t.push({tag:n.value,lIndex:n.lIndex,value:e})}));else{if("placeholder"!==n.type)return;u=o.getValueAsync(n.value,{part:n}).then((function(t){return null==t?e.nullGetter(n):t})).then((function(e){return t.push({tag:n.value,lIndex:n.lIndex,value:e}),e}))}return r.push(u.catch((function(e){e instanceof Array?s(a,e):a.push(e)}))),r}),[])).then((function(){return{errors:a,resolved:t}}))}},946:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var a=r(320),s=a.last,l=a.first;function u(e){this.name="GenericError",this.message=e,this.stack=new Error(e).stack}function p(e){this.name="TemplateError",this.message=e,this.stack=new Error(e).stack}function c(e){this.name="RenderingError",this.message=e,this.stack=new Error(e).stack}function f(e){this.name="ScopeParserError",this.message=e,this.stack=new Error(e).stack}function h(e){this.name="InternalError",this.properties={explanation:"InternalError"},this.message=e,this.stack=new Error(e).stack}function d(e){this.name="APIVersionError",this.properties={explanation:"APIVersionError"},this.message=e,this.stack=new Error(e).stack}u.prototype=Error.prototype,p.prototype=new u,c.prototype=new u,f.prototype=new u,h.prototype=new u,d.prototype=new u,e.exports={XTError:u,XTTemplateError:p,XTInternalError:h,XTScopeParserError:f,XTAPIVersionError:d,RenderingError:c,XTRenderingError:c,getClosingTagNotMatchOpeningTag:function(e){var t=e.tags,r=new p("Closing tag does not match opening tag");return r.properties={id:"closing_tag_does_not_match_opening_tag",explanation:'The tag "'.concat(t[0].value,'" is closed by the tag "').concat(t[1].value,'"'),openingtag:l(t).value,offset:[l(t).offset,s(t).offset],closingtag:s(t).value},r},getLoopPositionProducesInvalidXMLError:function(e){var t=e.tag,r=e.offset,n=new p('The position of the loop tags "'.concat(t,'" would produce invalid XML'));return n.properties={xtag:t,id:"loop_position_invalid",explanation:'The tags "'.concat(t,'" are misplaced in the document, for example one of them is in a table and the other one outside the table'),offset:r},n},getScopeCompilationError:function(e){var t=e.tag,r=e.rootError,n=e.offset,o=new f("Scope parser compilation failed");return o.properties={id:"scopeparser_compilation_failed",offset:n,xtag:t,explanation:'The scope parser for the tag "'.concat(t,'" failed to compile'),rootError:r},o},getScopeParserExecutionError:function(e){var t=e.tag,r=e.scope,n=e.error,o=e.offset,i=new f("Scope parser execution failed");return i.properties={id:"scopeparser_execution_failed",explanation:"The scope parser for the tag ".concat(t," failed to execute"),scope:r,offset:o,xtag:t,rootError:n},i},getUnclosedTagException:function(e){var t=new p("Unclosed tag");return t.properties={xtag:l(e.xtag.split(" ")).substr(1),id:"unclosed_tag",context:e.xtag,offset:e.offset,lIndex:e.lIndex,explanation:'The tag beginning with "'.concat(e.xtag.substr(0,10),'" is unclosed')},t},getUnopenedTagException:function(e){var t=new p("Unopened tag");return t.properties={xtag:s(e.xtag.split(" ")),id:"unopened_tag",context:e.xtag,offset:e.offset,lIndex:e.lIndex,explanation:'The tag beginning with "'.concat(e.xtag.substr(0,10),'" is unopened')},t},getUnmatchedLoopException:function(e){var t=e.location,r=e.offset,n=e.square,o="start"===t?"unclosed":"unopened",i=new p("".concat("start"===t?"Unclosed":"Unopened"," loop")),a=e.value;return i.properties={id:"".concat(o,"_loop"),explanation:'The loop with tag "'.concat(a,'" is ').concat(o),xtag:a,offset:r},n&&(i.properties.square=n),i},getDuplicateCloseTagException:function(e){var t=new p("Duplicate close tag, expected one close tag");return t.properties={xtag:l(e.xtag.split(" ")),id:"duplicate_close_tag",context:e.xtag,offset:e.offset,lIndex:e.lIndex,explanation:'The tag ending with "'.concat(e.xtag.substr(0,10),'" has duplicate close tags')},t},getDuplicateOpenTagException:function(e){var t=new p("Duplicate open tag, expected one open tag");return t.properties={xtag:l(e.xtag.split(" ")),id:"duplicate_open_tag",context:e.xtag,offset:e.offset,lIndex:e.lIndex,explanation:'The tag beginning with "'.concat(e.xtag.substr(0,10),'" has duplicate open tags')},t},getCorruptCharactersException:function(e){var t=e.tag,r=e.value,n=e.offset,o=new c("There are some XML corrupt characters");return o.properties={id:"invalid_xml_characters",xtag:t,value:r,offset:n,explanation:"There are some corrupt characters for the field ".concat(t)},o},getInvalidRawXMLValueException:function(e){var t=e.tag,r=e.value,n=e.offset,o=new c("Non string values are not allowed for rawXML tags");return o.properties={id:"invalid_raw_xml_value",xtag:t,value:r,offset:n,explanation:"The value of the raw tag : '".concat(t,"' is not a string")},o},getUnbalancedLoopException:function(e,t){var r=new p("Unbalanced loop tag"),n=t[0].part.value,o=t[1].part.value,i=e[0].part.value,a=e[1].part.value;return r.properties={id:"unbalanced_loop_tags",explanation:"Unbalanced loop tags {#".concat(n,"}{/").concat(o,"}{#").concat(i,"}{/").concat(a,"}"),offset:[t[0].part.offset,e[1].part.offset],lastPair:{left:t[0].part.value,right:t[1].part.value},pair:{left:e[0].part.value,right:e[1].part.value}},r},throwApiVersionError:function(e,t){var r=new d(e);throw r.properties=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({id:"api_version_error"},t),r},throwFileTypeNotHandled:function(e){var t=new h('The filetype "'.concat(e,'" is not handled by docxtemplater'));throw t.properties={id:"filetype_not_handled",explanation:'The file you are trying to generate is of type "'.concat(e,'", but only docx and pptx formats are handled'),fileType:e},t},throwFileTypeNotIdentified:function(e){var t,r=Object.keys(e.files).slice(0,10);t=0===r.length?"Empty zip file":"Zip file contains : ".concat(r.join(","));var n=new h("The filetype for this file could not be identified, is this file corrupted ? ".concat(t));throw n.properties={id:"filetype_not_identified",explanation:"The filetype for this file could not be identified, is this file corrupted ? ".concat(t)},n},throwMalformedXml:function(){var e=new h("Malformed xml");throw e.properties={explanation:"The template contains malformed xml",id:"malformed_xml"},e},throwMultiError:function(e){var t=new p("Multi error");throw t.properties={errors:e,id:"multi_error",explanation:"The template has multiple errors"},t},throwExpandNotFound:function(e){var t=e.part,r=t.value,n=t.offset,o=e.id,i=void 0===o?"raw_tag_outerxml_invalid":o,a=e.message,s=void 0===a?"Raw tag not in paragraph":a,l=e.part,u=e.explanation,c=void 0===u?'The tag "'.concat(r,'" is not inside a paragraph'):u;"function"==typeof c&&(c=c(l));var f=new p(s);throw f.properties={id:i,explanation:c,rootError:e.rootError,xtag:r,offset:n,postparsed:e.postparsed,expandTo:e.expandTo,index:e.index},f},throwRawTagShouldBeOnlyTextInParagraph:function(e){var t=new p("Raw tag should be the only text in paragraph"),r=e.part.value;throw t.properties={id:"raw_xml_tag_should_be_only_text_in_paragraph",explanation:'The raw tag "'.concat(r,'" should be the only text in this paragraph. This means that this tag should not be surrounded by any text or spaces.'),xtag:r,offset:e.part.offset,paragraphParts:e.paragraphParts},t},throwUnimplementedTagType:function(e,t){var r='Unimplemented tag type "'.concat(e.type,'"');e.module&&(r+=' "'.concat(e.module,'"'));var n=new p(r);throw n.properties={part:e,index:t,id:"unimplemented_tag_type"},n},throwXmlTagNotFound:function(e){var t=new p('No tag "'.concat(e.element,'" was found at the ').concat(e.position)),r=e.parsed[e.index];throw t.properties={id:"no_xml_tag_found_at_".concat(e.position),explanation:'No tag "'.concat(e.element,'" was found at the ').concat(e.position),offset:r.offset,part:r,parsed:e.parsed,index:e.index,element:e.element},t},throwXmlInvalid:function(e,t){var r=new p("An XML file has invalid xml");throw r.properties={id:"file_has_invalid_xml",content:e,offset:t,explanation:"The docx contains invalid XML, it is most likely corrupt"},r},throwResolveBeforeCompile:function(){var e=new h("You must run `.compile()` before running `.resolveData()`");throw e.properties={id:"resolve_before_compile",explanation:"You must run `.compile()` before running `.resolveData()`"},e},throwRenderInvalidTemplate:function(){var e=new h("You should not call .render on a document that had compilation errors");throw e.properties={id:"render_on_invalid_template",explanation:"You should not call .render on a document that had compilation errors"},e},throwRenderTwice:function(){var e=new h("You should not call .render twice on the same docxtemplater instance");throw e.properties={id:"render_twice",explanation:"You should not call .render twice on the same docxtemplater instance"},e}}}},t={},r=function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}(807);window.docxtemplater=r}();