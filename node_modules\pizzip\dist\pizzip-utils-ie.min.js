(()=>{"use strict";var r={};r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(r){if("object"==typeof window)return window}}(),document.write("\x3c!-- IEBinaryToArray_ByteStr --\x3e\r\n<script type='text/vbscript'>\r\nFunction IEBinaryToArray_ByteStr(Binary)\r\n   IEBinaryToArray_ByteStr = CStr(Binary)\r\nEnd Function\r\nFunction IEBinaryToArray_ByteStr_Last(Binary)\r\n   Dim lastIndex\r\n   lastIndex = LenB(Binary)\r\n   if lastIndex mod 2 Then\r\n       IEBinaryToArray_ByteStr_Last = Chr( AscB( MidB( Binary, lastIndex, 1 ) ) )\r\n   Else\r\n       IEBinaryToArray_ByteStr_Last = \"\"\r\n   End If\r\nEnd Function\r\n<\/script>\r\n"),r.g.PizZipUtils._getBinaryFromXHR=function(r){for(var n=r.responseBody,t={},i=0;i<256;i++)for(var a=0;a<256;a++)t[String.fromCharCode(i+(a<<8))]=String.fromCharCode(i)+String.fromCharCode(a);var e=IEBinaryToArray_ByteStr(n),o=IEBinaryToArray_ByteStr_Last(n);return e.replace(/[\s\S]/g,(function(r){return t[r]}))+o}})();