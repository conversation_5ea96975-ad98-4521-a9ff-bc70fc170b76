(()=>{"use strict";var t={593:t=>{var e={};function r(){try{return new window.XMLHttpRequest}catch(t){}}e._getBinaryFromXHR=function(t){return t.response||t.responseText};var n=window.ActiveXObject?function(){return r()||function(){try{return new window.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}()}:r;e.getBinaryContent=function(t,r){try{var o=n();o.open("GET",t,!0);var i=function(t){return t.startsWith("file://")||"undefined"!=typeof window&&"file:"===window.location.protocol}(t);"responseType"in o&&(o.responseType="arraybuffer"),o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.onreadystatechange=function(n){if(4===o.readyState)if(200===o.status||i&&0===o.status)try{var s=e._getBinaryFromXHR(o);r(null,s)}catch(t){r(new Error(t),null)}else{var a=0===o.status?"Server not responding or CORS headers missing. ":"";r(new Error("Ajax error for ".concat(t,": status ").concat(o.status," ").concat(a).concat(o.statusText)),null)}},o.send()}catch(t){r(new Error(t),null)}},t.exports=e}},e={},r=function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,r),i.exports}(593);window.PizZipUtils=r})();