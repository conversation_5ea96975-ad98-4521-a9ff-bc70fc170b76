import zipfile
import xml.etree.ElementTree as ET
import re

# Extraer variables del documento Word
with zipfile.ZipFile('plantilla_word/QC-1000-P770877-1185305.docx', 'r') as docx:
    document_xml = docx.read('word/document.xml')
    content = document_xml.decode('utf-8')

# Buscar variables del Word
tag_pattern = r'<w:tag w:val="([^"]+)"/>'
alias_pattern = r'<w:alias w:val="([^"]+)"/>'

tags = re.findall(tag_pattern, content)
aliases = re.findall(alias_pattern, content)

# Crear mapeo de variables del Word
word_variables = {}
for i in range(min(len(tags), len(aliases))):
    word_variables[tags[i]] = aliases[i]

# Variables del formulario PW1100 (extraídas manualmente del HTML)
pw1100_fields = {
    # Información general
    'aircraft_model': 'Aircraft Model (A320 NEO / A321 NEO)',
    'aircraft_registration': 'Aircraft Registration',
    'engine_sn': 'Engine S/N',
    'inspector_stamp': 'Inspector Stamp',
    
    # LPC Section
    'lpc_stage1_status': 'LPC STAGE 1',
    'lpc_stage1_remarks': 'LPC 1 Finding / Remarks',
    'lpc_stage2_status': 'LPC STAGE 2', 
    'lpc_stage2_remarks': 'LPC 2 Finding / Remarks',
    'lpc_stage3_status': 'LPC STAGE 3',
    'lpc_stage3_remarks': 'LPC 3 Finding / Remarks',
    
    # #3 Bearing
    'bearing3_front_status': '# 3 BEARING FRONT SEAL',
    'bearing3_front_remarks': '# 3 Bearing front Seal Finding / Remarks',
    'bearing3_rear_status': '# 3 BEARING REAR SEAL',
    'bearing3_rear_remarks': '# 3 Bearing rear Seal Finding / Remarks',
    
    # HPC Section (8 stages)
    'hpc_stage1_status': 'HPC STAGE 1',
    'hpc_stage1_remarks': 'HPC Stage 1 Finding / Remarks',
    'hpc_stage2_status': 'HPC STAGE 2',
    'hpc_stage2_remarks': 'HPC Stage 2 Finding / Remarks',
    'hpc_stage3_status': 'HPC STAGE 3',
    'hpc_stage3_remarks': 'HPC Stage 3 Finding / Remarks',
    'hpc_stage4_status': 'HPC STAGE 4',
    'hpc_stage4_remarks': 'HPC Stage 4 Finding / Remarks',
    'hpc_stage5_status': 'HPC STAGE 5',
    'hpc_stage5_remarks': 'HPC Stage 5 Finding / Remarks',
    'hpc_stage6_status': 'HPC STAGE 6',
    'hpc_stage6_remarks': 'HPC Stage 6 Finding / Remarks',
    'hpc_stage7_status': 'HPC STAGE 7',
    'hpc_stage7_remarks': 'HPC Stage 7 Finding / Remarks',
    'hpc_stage8_status': 'HPC STAGE 8',
    'hpc_stage8_remarks': 'HPC Stage 8 Finding / Remarks',
    
    # Combustion Chamber
    'igniter_status': 'IGNITER SEGMENT',
    'igniter_remarks': 'Igniter Segment Finding / Remarks',
    'fuelnozzle_status': 'FUEL NOZZLE',
    'fuelnozzle_remarks': 'Fuel Nozzle Finding / Remarks',
    'cch_inner_status': 'CCH INNER LINER',
    'cch_inner_remarks': 'CCH Inner Liner Finding / Remarks',
    'cch_outer_status': 'CCH OUTER LINER',
    'cch_outer_remarks': 'CCH Outer Liner Finding / Remarks',
    
    # Ship Lap
    'shiplap_status': 'SHIPLAP',
    'shiplap_remarks': 'Shiplap Finding / Remarks',
    'shiplap_dimensions': 'Ship Lap Dimensions',
    
    # HPT Section
    'hpt_vane_status': 'HPT VANE',
    'hpt_vane_remarks': 'HPT VANE Finding / Remarks',
    'hpt_s1_status': 'HPT STAGE 1',
    'hpt_s1_remarks': 'HPT Stage 1 Finding / Remarks',
    'hpt_s2_status': 'HPT STAGE 2',
    'hpt_s2_remarks': 'HPT Stage 2 Finding / Remarks',
    
    # LPT Section
    'lpt_s1_status': 'LPT STAGE 1',
    'lpt_s1_remarks': 'LPT Stage 1 Finding / Remarks',
    'lpt_s2_status': 'LPT STAGE 2',
    'lpt_s2_remarks': 'LPT Stage 2 Finding / Remarks',
    'lpt_s3_status': 'LPT STAGE 3',
    'lpt_s3_remarks': 'LPT Stage 3 Finding / Remarks',
    
    # Final Disposition
    'final_disposition': 'Final disposition, Restrictions or New Restrictions',
    'engine_status_bsi': 'Engine Status after BSI'
}

print('=== COMPARACIÓN FORMULARIO PW1100 vs DOCUMENTO WORD ===')
print()

# Buscar coincidencias
matches = []
word_aliases_lower = {alias.lower(): tag for tag, alias in word_variables.items()}

print('✅ CAMPOS QUE COINCIDEN:')
for form_field, form_desc in pw1100_fields.items():
    form_desc_lower = form_desc.lower()
    
    # Buscar coincidencias exactas o similares
    for word_alias_lower, word_tag in word_aliases_lower.items():
        if (form_desc_lower in word_alias_lower or 
            word_alias_lower in form_desc_lower or
            form_desc_lower.replace('/', '').replace(' ', '') == word_alias_lower.replace('/', '').replace(' ', '')):
            
            matches.append((form_field, form_desc, word_tag, word_variables[word_tag]))
            print(f'  • {form_field} → {word_tag}')
            print(f'    Form: "{form_desc}"')
            print(f'    Word: "{word_variables[word_tag]}"')
            print()
            break

print(f'TOTAL DE COINCIDENCIAS: {len(matches)}')
print()

# Variables del Word que no tienen equivalente en el formulario
print('⚠️  VARIABLES DEL WORD SIN EQUIVALENTE EN EL FORMULARIO:')
matched_word_tags = [match[2] for match in matches]
unmatched_word = [(tag, alias) for tag, alias in word_variables.items() if tag not in matched_word_tags]

for tag, alias in unmatched_word:
    print(f'  • {tag} → "{alias}"')

print(f'\nTOTAL SIN EQUIVALENTE: {len(unmatched_word)}')
print()

# Variables del formulario que no tienen equivalente en el Word
print('⚠️  CAMPOS DEL FORMULARIO SIN EQUIVALENTE EN EL WORD:')
matched_form_fields = [match[0] for match in matches]
unmatched_form = [(field, desc) for field, desc in pw1100_fields.items() if field not in matched_form_fields]

for field, desc in unmatched_form:
    print(f'  • {field} → "{desc}"')

print(f'\nTOTAL SIN EQUIVALENTE: {len(unmatched_form)}')
