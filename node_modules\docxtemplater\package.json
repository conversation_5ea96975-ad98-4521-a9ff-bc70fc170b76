{"name": "docxtemplater", "version": "3.63.0", "author": "<PERSON>", "description": "Generate docx, pptx, and xlsx from templates (Word, Powerpoint and Excel documents), from Node.js, the Browser and the command line", "contributors": [{"name": "<PERSON>"}], "main": "js/docxtemplater.js", "keywords": ["docx", "pptx", "templates", "template", "templater", "templating", "report", "xlsx", "generation", "generate", "generator", "microsoft office", "microsoft word", "microsoft powerpoint", "microsoft excel", "create", "make", "Office Open XML", "creator"], "types": "./js/docxtemplater.d.ts", "repository": {"type": "git", "url": "git+https://github.com/open-xml-templating/docxtemplater.git"}, "dependencies": {"@xmldom/xmldom": "^0.9.8"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.4", "@babel/cli": "^7.26.4", "@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.26.10", "@babel/preset-env": "^7.26.9", "@stryker-mutator/core": "^8.7.1", "@stryker-mutator/mocha-runner": "^8.7.1", "@stylistic/eslint-plugin-js": "^4.2.0", "angular-expressions": "^1.4.3", "babel-loader": "^10.0.0", "chai": "^4.5.0", "chalk": "^5.4.1", "cross-env": "^7.0.3", "diff": "^7.0.0", "envify": "^4.1.0", "es6-promise": "^4.2.8", "eslint": "^9.23.0", "eslint_d": "^14.3.0", "eslint-formatter-unix": "^8.40.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-sort-class-members": "^1.21.0", "eslint-plugin-specific-sort": "file:.eslint-rules", "finalhandler": "^2.1.0", "globals": "^16.0.0", "lodash": "^4.17.21", "mkdirp": "^3.0.1", "mocha": "^10.8.2", "nyc": "^17.1.0", "pizzip": "^3.2.0", "playwright": "^1.51.1", "prettier": "^3.5.3", "rimraf": "^6.0.1", "selenium-standalone": "^10.0.1", "serve-static": "^1.15.0", "stryker-cli": "^1.0.2", "tsd": "^0.31.2", "updtr": "^4.1.0", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}, "license": "MIT", "engines": {"node": ">=0.10"}, "scripts": {"profile": "bash ./profile.bash", "preversion": "bash ./preversion.bash", "check-casing": "bash ./check-casing.bash", "compile": "npm run convertto:es5", "browserify:test": "cross-env FILE=test webpack", "browserify:lib": "webpack", "browserify:min": "cross-env MIN=true webpack", "updtr": "updtr --exclude chai mocha", "browserify": "npm run browserify:test && npm run browserify:lib && npm run browserify:min", "convertto:es5": "rimraf js -rf && mkdirp js && npm run babel && cp es6/*.ts js", "convertto:es5:watch": "npm run babel -- --watch", "test:coverage": "nyc --reporter=html --reporter=text mocha --  es6/tests/index.js", "prettier": "prettier --cache --list-different \"es6/**/!(filenames).js\" \"*.js\" \"*.mjs\" \"es6/*.ts\" \".eslint-rules/*.js\" README.md CHANGELOG.md", "prettier:fix": "prettier --cache --write \"es6/**/!(filenames).js\" \"*.js\" \"*.mjs\" \"es6/*.ts\" \".eslint-rules/*.js\" README.md CHANGELOG.md", "lint": "eslint_d . --cache && bash ./check-casing.bash && npm run prettier && shfmt -l *.bash", "lint:fix": "npm run prettier:fix && bash ./check-casing.bash && eslint_d . --cache && shfmt -l -w *.bash", "test:chrome": "cross-env BROWSER=CHROME bash ./webdriver.bash", "test:firefox": "cross-env BROWSER=FIREFOX bash ./webdriver.bash", "test:browser": "bash ./webdriver.bash", "babel": "babel es6 --out-dir js", "mocha": "mocha --full-trace --check-leaks js/tests/index.js", "test:es6": "mocha --full-trace --check-leaks es6/tests/index.js", "test:es6:update-fixtures": "cross-env UPDATE=true mocha --full-trace --check-leaks es6/tests/index.js", "test:es6:update-snapshots": "cross-env WRITE_SNAPSHOTS=true mocha --full-trace --check-leaks es6/tests/index.js", "test:es6:fast": "cross-env FAST=true mocha --full-trace --check-leaks es6/tests/index.js", "test:watch": "cross-env FAST=true mocha --watch --full-trace --check-leaks es6/tests/index.js", "test:es6:slow": "cross-env FAST= mocha --full-trace --check-leaks es6/tests/index.js", "test": "npm run convertto:es5 && npm run mocha", "test:es5": "npm test", "test:typings": "cp es6/*.ts js && cp es6/*.ts . && tsd .", "test:mutations": "cross-env SPEED_TEST=no stryker run", "memorytest": "npm run compile && node --max-old-space-size=8000 js/memory-test.js", "verifypublishsize": "bash ./verifypublishsize.bash"}, "tsd": {"compilerOptions": {"lib": ["DOM"]}}}