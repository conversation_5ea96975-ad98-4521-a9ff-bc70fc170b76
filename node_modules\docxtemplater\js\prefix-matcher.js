"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
var nbspRegex = new RegExp(String.fromCharCode(160), "g");
function replaceNbsps(str) {
  return str.replace(nbspRegex, " ");
}
function match(condition, placeHolderContent) {
  var type = _typeof(condition);
  if (type === "string") {
    return replaceNbsps(placeHolderContent.substr(0, condition.length)) === condition;
  }
  if (condition instanceof RegExp) {
    return condition.test(replaceNbsps(placeHolderContent));
  }
  if (type === "function") {
    return !!condition(placeHolderContent);
  }
}
function getValue(condition, placeHolderContent) {
  var type = _typeof(condition);
  if (type === "string") {
    return replaceNbsps(placeHolderContent).substr(condition.length);
  }
  if (condition instanceof RegExp) {
    return replaceNbsps(placeHolderContent).match(condition)[1];
  }
  if (type === "function") {
    return condition(placeHolderContent);
  }
}
function getValues(condition, placeHolderContent) {
  var type = _typeof(condition);
  if (type === "string") {
    return [placeHolderContent, replaceNbsps(placeHolderContent).substr(condition.length)];
  }
  if (condition instanceof RegExp) {
    return replaceNbsps(placeHolderContent).match(condition);
  }
  if (type === "function") {
    return [placeHolderContent, condition(placeHolderContent)];
  }
}
module.exports = {
  match: match,
  getValue: getValue,
  getValues: getValues
};