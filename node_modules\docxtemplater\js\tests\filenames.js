module.exports=["a16-row-id.pptx","angular-example.docx","assignment.docx","bug-closing-placeholder-orphan.docx","comment-with-loop.docx","cond-image-no-innertext-before.docx","cond-image-no-innertext.docx","cond-image.docx","core-xml-missing-close-tag.docx","cyrillic.docx","delimiter-gt.docx","delimiter-pct.docx","empty-loop-regression.docx","empty.zip","errors-footer-and-header.docx","expected-12.pptx","expected-a16-row-id.pptx","expected-assignment.docx","expected-bug-closing-placeholder-orphan.docx","expected-comment-example.docx","expected-comments.docx","expected-cond-image-no-innertext-before.docx","expected-cond-image-no-innertext.docx","expected-cond-image.docx","expected-core-xml.docx","expected-docm.docm","expected-dotm.dotm","expected-dotx.dotx","expected-empty-table.docx","expected-empty.pptx","expected-header-without-digit.docx","expected-john-doe.pptx","expected-loop-example.pptx","expected-loop-hebrew.docx","expected-loop-images-footer.docx","expected-loop-images.docx","expected-loop-raw-xml.docx","expected-loop-regression.docx","expected-loop-table.pptx","expected-loop-valid.docx","expected-loop-with-continuous-section-break-2.docx","expected-loop-with-continuous-section-break.docx","expected-loop-with-section-break-after.docx","expected-module-change-rels.docx","expected-multi-loop.docx","expected-multi-section.docx","expected-multiline-indent.docx","expected-multiline.docx","expected-multiline.pptx","expected-nextpage-section-break.docx","expected-no-multiline.docx","expected-no-multiline.pptx","expected-no-proofstate.docx","expected-noparagraph-loop-with-pagebreak.docx","expected-office365.docx","expected-page-break-3-els-std-loop.docx","expected-page-break-falsy-std-loop.docx","expected-page-break-truthy-std-loop.docx","expected-page-section-break.docx","expected-pagebreak-table-loop.docx","expected-paragraph-loop-empty-with-pagebreak.docx","expected-paragraph-loop-kept-section.docx","expected-paragraph-loop-with-pagebreak.docx","expected-paragraph-loop-with-section-break-after.docx","expected-paragraph-loop.docx","expected-paragraph-loop.pptx","expected-pptm.pptm","expected-proofstate-removed.docx","expected-quotes-in-tag.docx","expected-raw-xml-after-table.docx","expected-raw-xml-example.pptx","expected-raw-xml-null.docx","expected-raw-xml.docx","expected-regression-complex-loops.docx","expected-regression-loops-resolve-long.docx","expected-regression-loops-resolve.docx","expected-regression-multiline.pptx","expected-rendered-hello.docx","expected-rendered-par-in-par.docx","expected-run-props-linebreak.pptx","expected-sdt-content.docx","expected-sdtcontent-valid.docx","expected-self-closing-w-sdtcontent.docx","expected-smart-art.docx","expected-smart-art.pptx","expected-spacing-end.docx","expected-tab-character.pptx","expected-table-3-cells.pptx","expected-table-3-true-cells.pptx","expected-table-empty.docx","expected-table-example.pptx","expected-table-in-table-corruption.docx","expected-table-unbalanced-loop-2.docx","expected-table-unbalanced-loop.docx","expected-tag-docprops-in-doc.docx","expected-tag-docprops.docx","expected-tag-example.docx","expected-tag-in-title.pptx","expected-tag-intelligent-loop-table.docx","expected-tag-justified.docx","expected-tag-spanning-multiline.docx","expected-tag-with-comment.docx","expected-two-multiline.docx","expected-users.docx","expected-with-default-contenttype.docx","expected-with-page-break-3-els.docx","expected-with-page-break-falsy-without-paragraph-loop.docx","expected-with-page-break-falsy.docx","expected-with-page-break-truthy.docx","gt-delimiters.docx","header-without-digit.docx","input.docm","input.dotm","input.dotx","input.pptm","justify.docx","loop-example.pptx","loop-hebrew.docx","loop-image-footer.docx","loop-image.docx","loop-table-no-header.pptx","loop-table.pptx","loop-valid.docx","loop-with-continuous-section-break.docx","loop-with-page-section-break.docx","loop-with-section-break-after.docx","loop-with-section.docx","loops-with-table-raw-xml.docx","memory-stress.docx","multi-errors.docx","multi-level.docx","multi-loop.docx","multi-page-to-merge.pptx","multi-page.pptx","multi-tags.docx","nextpage-section-break.docx","office365.docx","one-raw-xml-tag.docx","page-break-inside-condition.docx","pagebreak-table-loop.docx","paragraph-loop-error.docx","paragraph-loop-with-pagebreak.docx","paragraph-loop-with-section-break-after.docx","paragraph-loop.pptx","paragraph-loops.docx","properties.docx","quotes-in-tag.docx","raw-xml-after-table.docx","raw-xml-example.pptx","regression-complex-loops.docx","regression-dash-loop-in-table-cell.pptx","regression-loops-resolve.docx","regression-par-in-par.docx","regression-sdtcontent-paragraph.docx","run-props-linebreak.pptx","sdt-content.docx","self-closing-w-sdtcontent.docx","simple-example.pptx","simple-zip.zip","simple.xlsx","smart-art.docx","smart-art.pptx","spacing-end.docx","tab-character.pptx","table-complex-example.docx","table-complex2-example.docx","table-empty.docx","table-example.pptx","table-in-table-corruption.docx","table-loop.docx","table-raw-xml.docx","table-repeat.docx","table-unbalanced-loop-2.docx","table-unbalanced-loop.docx","tag-dash-loop-list.docx","tag-dash-loop-table.docx","tag-dash-loop.docx","tag-docprops-in-doc.docx","tag-docprops.docx","tag-example.docx","tag-formating.docx","tag-in-title.pptx","tag-intelligent-loop-table.docx","tag-inverted-loop-example.docx","tag-loop-example.docx","tag-looping.docx","tag-multiline.docx","tag-multiline.pptx","tag-product-loop.docx","tag-spanning-multiline.docx","tag-spanning-multiple-lines.docx","tag-spanning-multiple-lines.pptx","tag-with-comment.docx","test.odt","text-example.docx","title-example.pptx","two-tags.docx","users.docx","with-comments.docx","with-default-contenttype.docx","xml-insertion-example.docx"]