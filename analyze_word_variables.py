import zipfile
import xml.etree.ElementTree as ET
import re

# Extraer el contenido XML del documento Word
with zipfile.ZipFile('plantilla_word/QC-1000-P770877-1185305.docx', 'r') as docx:
    document_xml = docx.read('word/document.xml')
    content = document_xml.decode('utf-8')

# Buscar todas las variables/campos de datos
# Los campos están definidos con w:tag y w:alias
tag_pattern = r'<w:tag w:val="([^"]+)"/>'
alias_pattern = r'<w:alias w:val="([^"]+)"/>'
placeholder_pattern = r'\[([^\]]+)\]'

tags = re.findall(tag_pattern, content)
aliases = re.findall(alias_pattern, content)
placeholders = re.findall(placeholder_pattern, content)

print('=== VARIABLES IDENTIFICADAS EN EL DOCUMENTO WORD ===')
print()
print('1. TAGS (nombres técnicos de variables):')
for i, tag in enumerate(tags, 1):
    print(f'   {i}. {tag}')

print()
print('2. ALIAS (nombres descriptivos):')
for i, alias in enumerate(aliases, 1):
    print(f'   {i}. {alias}')

print()
print('3. PLACEHOLDERS (texto visible):')
for i, placeholder in enumerate(placeholders, 1):
    print(f'   {i}. {placeholder}')

# Crear mapeo de variables
print()
print('=== MAPEO DE VARIABLES ===')
print()
variable_mapping = {}
for i in range(min(len(tags), len(aliases))):
    variable_mapping[tags[i]] = aliases[i]
    print(f'{i+1}. Tag: {tags[i]}')
    print(f'   Alias: {aliases[i]}')
    print()

# Buscar texto estático importante
print('=== TEXTO ESTÁTICO ENCONTRADO ===')
static_texts = [
    'COVER SHEET',
    'AIRCRAFT MODEL',
    'ENGINE S/N',
    'A/C REGISTRATION', 
    'INSPECTOR STAMP',
    'LPC STAGE',
    'LPT STAGE',
    'FINAL DISPOSITION',
    'ENGINE STATUS AFTER BSI',
    'SUPPORT IMAGES'
]

for text in static_texts:
    if text in content:
        print(f'✓ {text}')
    else:
        print(f'✗ {text}')
