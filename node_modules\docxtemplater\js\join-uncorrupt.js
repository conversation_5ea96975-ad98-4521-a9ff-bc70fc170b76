"use strict";

var _require = require("./doc-utils.js"),
  startsWith = _require.startsWith,
  endsWith = _require.endsWith,
  isStarting = _require.isStarting,
  isEnding = _require.isEnding,
  isWhiteSpace = _require.isWhiteSpace;
var filetypes = require("./filetypes.js");
function addEmptyParagraphAfterTable(parts) {
  var lastNonEmpty = "";
  for (var i = 0, len = parts.length; i < len; i++) {
    var p = parts[i];
    if (isWhiteSpace(p) || startsWith(p, "<w:bookmarkEnd")) {
      continue;
    }
    if (endsWith(lastNonEmpty, "</w:tbl>")) {
      if (!startsWith(p, "<w:p") && !startsWith(p, "<w:tbl") && !startsWith(p, "<w:sectPr")) {
        p = "<w:p/>".concat(p);
      }
    }
    lastNonEmpty = p;
    parts[i] = p;
  }
  return parts;
}

// eslint-disable-next-line complexity
function joinUncorrupt(parts, options) {
  var contains = options.fileTypeConfig.tagShouldContain || [];
  /*
   * Before doing this "uncorruption" method here, this was done with the
   * `part.emptyValue` trick, however, there were some corruptions that were
   * not handled, for example with a template like this :
   *
   * ------------------------------------------------
   * | {-w:p falsy}My para{/falsy}   |              |
   * | {-w:p falsy}My para{/falsy}   |              |
   * ------------------------------------------------
   */
  var collecting = "";
  var currentlyCollecting = -1;
  if (filetypes.docx.indexOf(options.contentType) !== -1) {
    parts = addEmptyParagraphAfterTable(parts);
  }
  var startIndex = -1;
  for (var j = 0, len2 = contains.length; j < len2; j++) {
    var _contains$j = contains[j],
      tag = _contains$j.tag,
      shouldContain = _contains$j.shouldContain,
      value = _contains$j.value,
      drop = _contains$j.drop,
      dropParent = _contains$j.dropParent;
    for (var i = 0, len = parts.length; i < len; i++) {
      var part = parts[i];
      if (currentlyCollecting === j) {
        if (isEnding(part, tag)) {
          currentlyCollecting = -1;
          if (dropParent) {
            var start = -1;
            for (var k = startIndex; k > 0; k--) {
              if (isStarting(parts[k], dropParent)) {
                start = k;
                break;
              }
            }
            for (var _k = start; _k <= parts.length; _k++) {
              if (isEnding(parts[_k], dropParent)) {
                parts[_k] = "";
                break;
              }
              parts[_k] = "";
            }
          } else {
            for (var _k2 = startIndex; _k2 <= i; _k2++) {
              parts[_k2] = "";
            }
            if (!drop) {
              parts[i] = collecting + value + part;
            }
          }
        }
        collecting += part;
        for (var _k3 = 0, len3 = shouldContain.length; _k3 < len3; _k3++) {
          var sc = shouldContain[_k3];
          if (isStarting(part, sc)) {
            currentlyCollecting = -1;
            break;
          }
        }
      }
      if (currentlyCollecting === -1 && isStarting(part, tag) &&
      /*
       * To verify that the part doesn't have multiple tags,
       * such as <w:tc><w:p>
       */
      part.substr(1).indexOf("<") === -1) {
        // self-closing tag such as <w:t/>
        if (part[part.length - 2] === "/") {
          parts[i] = "";
        } else {
          startIndex = i;
          currentlyCollecting = j;
          collecting = part;
        }
      }
    }
  }
  return parts;
}
module.exports = joinUncorrupt;