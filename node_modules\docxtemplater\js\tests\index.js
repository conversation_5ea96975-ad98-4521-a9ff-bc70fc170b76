"use strict";

require("es6-promise").polyfill();
var _require = require("./utils.js"),
  expect = _require.expect,
  setExamplesDirectory = _require.setExamplesDirectory,
  setSnapshotFile = _require.setSnapshotFile,
  setStartFunction = _require.setStartFunction,
  shouldBeSame = _require.shouldBeSame,
  start = _require.start,
  createDocV4 = _require.createDocV4;
var path = require("path");
if (path.resolve) {
  setExamplesDirectory(path.resolve(__dirname, "..", "..", "examples"));
  setSnapshotFile(path.resolve(__dirname, "__snapshots.js"));
}
setStartFunction(startTest, require("./__snapshots.js"));
function startTest() {
  beforeEach(function () {
    this.renderV4 = function (_ref) {
      var _ref$async = _ref.async,
        async = _ref$async === void 0 ? false : _ref$async,
        _ref$name = _ref.name,
        name = _ref$name === void 0 ? "" : _ref$name,
        _ref$options = _ref.options,
        options = _ref$options === void 0 ? {} : _ref$options,
        _ref$expectedName = _ref.expectedName,
        expectedName = _ref$expectedName === void 0 ? "" : _ref$expectedName,
        _ref$expectedText = _ref.expectedText,
        expectedText = _ref$expectedText === void 0 ? "" : _ref$expectedText,
        _ref$data = _ref.data,
        data = _ref$data === void 0 ? {} : _ref$data;
      var doc = createDocV4(name, options);
      if (async) {
        return doc.renderAsync(data).then(function () {
          if (expectedText) {
            expect(doc.getFullText()).to.be.equal(expectedText);
          }
          if (expectedName) {
            shouldBeSame({
              doc: doc,
              expectedName: expectedName
            });
          }
          return doc;
        });
      }
      doc.render(data);
      if (expectedText) {
        expect(doc.getFullText()).to.be.equal(expectedText);
      }
      if (expectedName) {
        shouldBeSame({
          doc: doc,
          expectedName: expectedName
        });
      }
      return doc;
    };
    this.render = function (_ref2) {
      var _ref2$async = _ref2.async,
        async = _ref2$async === void 0 ? false : _ref2$async,
        _ref2$name = _ref2.name,
        name = _ref2$name === void 0 ? "" : _ref2$name,
        _ref2$options = _ref2.options,
        options = _ref2$options === void 0 ? {} : _ref2$options,
        _ref2$expectedName = _ref2.expectedName,
        expectedName = _ref2$expectedName === void 0 ? "" : _ref2$expectedName,
        _ref2$expectedText = _ref2.expectedText,
        expectedText = _ref2$expectedText === void 0 ? "" : _ref2$expectedText,
        _ref2$data = _ref2.data,
        data = _ref2$data === void 0 ? {} : _ref2$data;
      var doc = createDocV4(name, options);
      if (async) {
        return doc.renderAsync(data).then(function () {
          if (expectedText) {
            expect(doc.getFullText()).to.be.equal(expectedText);
          }
          if (expectedName) {
            shouldBeSame({
              doc: doc,
              expectedName: expectedName
            });
          }
          return doc;
        });
      }
      doc.render(data);
      if (expectedText) {
        expect(doc.getFullText()).to.be.equal(expectedText);
      }
      if (expectedName) {
        shouldBeSame({
          doc: doc,
          expectedName: expectedName
        });
      }
      return doc;
    };
  });
  describe("", function () {
    require("./e2e/text.js");
    require("./e2e/base.js");
    require("./e2e/xml-templater.js");
    require("./e2e/v3.js");
    require("./e2e/errors.js");
    require("./e2e/speed.js");
    require("./e2e/lexer-parser-render.js");
    require("./e2e/integration.js");
    require("./e2e/doc-props.js");
    require("./e2e/modules.js");
    require("./e2e/traits.js");
    require("./e2e/pptx.js");
    require("./e2e/table.js");
    require("./e2e/async.js");
    require("./unit/xml-matcher.js");
    require("./unit/doc-utils.js");
    require("./unit/expressions.js");
    require("./unit/merge-sort.js");
    require("./unit/scope-manager.js");
  });
}
start();