"use strict";

var _require = require("./errors.js"),
  throwUnimplementedTagType = _require.throwUnimplementedTagType,
  XTScopeParserError = _require.XTScopeParserError;
var _require2 = require("./doc-utils.js"),
  pushArray = _require2.pushArray;
var getResolvedId = require("./get-resolved-id.js");
function moduleRender(part, options) {
  for (var _i2 = 0, _options$modules2 = options.modules; _i2 < _options$modules2.length; _i2++) {
    var _module = _options$modules2[_i2];
    var moduleRendered = _module.render(part, options);
    if (moduleRendered) {
      return moduleRendered;
    }
  }
  return false;
}
function render(options) {
  var baseNullGetter = options.baseNullGetter;
  var compiled = options.compiled,
    scopeManager = options.scopeManager;
  options.nullGetter = function (part, sm) {
    return baseNullGetter(part, sm || scopeManager);
  };
  var errors = [];
  var parts = compiled.map(function (part, i) {
    options.index = i;
    options.resolvedId = getResolvedId(part, options);
    var moduleRendered;
    try {
      moduleRendered = moduleRender(part, options);
    } catch (e) {
      if (e instanceof XTScopeParserError) {
        errors.push(e);
        return part;
      }
      throw e;
    }
    if (moduleRendered) {
      if (moduleRendered.errors) {
        pushArray(errors, moduleRendered.errors);
      }
      return moduleRendered;
    }
    if (part.type === "content" || part.type === "tag") {
      return part;
    }
    throwUnimplementedTagType(part, i);
  }).reduce(function (parts, _ref) {
    var value = _ref.value;
    if (value instanceof Array) {
      pushArray(parts, value);
    } else if (value) {
      parts.push(value);
    }
    return parts;
  }, []);
  return {
    errors: errors,
    parts: parts
  };
}
module.exports = render;