"use strict";

function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function isPlaceholder(part) {
  return part.type === "placeholder";
}

/* eslint-disable-next-line complexity */
function getTags(postParsed) {
  var tags = {};
  var stack = [{
    items: postParsed.filter(isPlaceholder),
    parents: [],
    path: []
  }];
  function processFiltered(part, current, filtered) {
    if (filtered.length) {
      stack.push({
        items: filtered,
        parents: [].concat(_toConsumableArray(current.parents), [part]),
        path: part.dataBound !== false && !part.attrParsed && part.value && !part.attrParsed ? [].concat(_toConsumableArray(current.path), [part.value]) : _toConsumableArray(current.path)
      });
    }
  }
  function getLocalTags(tags, path) {
    var sizeScope = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : path.length;
    var localTags = tags;
    for (var i = 0; i < sizeScope; i++) {
      localTags = localTags[path[i]];
    }
    return localTags;
  }
  function getScopeSize(part, parents) {
    return parents.reduce(function (size, parent) {
      var lIndexLoop = typeof parent.lIndex === "number" ? parent.lIndex : parseInt(parent.lIndex.split("-")[0], 10);
      return lIndexLoop > part.lIndex ? size - 1 : size;
    }, parents.length);
  }
  while (stack.length > 0) {
    var current = stack.pop();
    var localTags = getLocalTags(tags, current.path);
    for (var _i2 = 0, _current$items2 = current.items; _i2 < _current$items2.length; _i2++) {
      var _localTags4, _part$value2;
      var part = _current$items2[_i2];
      if (part.attrParsed) {
        for (var key in part.attrParsed) {
          processFiltered(part, current, part.attrParsed[key].filter(isPlaceholder));
        }
        continue;
      }
      if (part.subparsed) {
        if (part.dataBound !== false) {
          var _localTags, _part$value;
          (_localTags = localTags)[_part$value = part.value] || (_localTags[_part$value] = {});
        }
        processFiltered(part, current, part.subparsed.filter(isPlaceholder));
        continue;
      }
      if (part.cellParsed) {
        for (var _i4 = 0, _part$cellPostParsed2 = part.cellPostParsed; _i4 < _part$cellPostParsed2.length; _i4++) {
          var cp = _part$cellPostParsed2[_i4];
          if (cp.type === "placeholder") {
            if (cp.module === "pro-xml-templating/xls-module-loop") {
              continue;
            } else if (cp.subparsed) {
              var _localTags2, _cp$value;
              (_localTags2 = localTags)[_cp$value = cp.value] || (_localTags2[_cp$value] = {});
              processFiltered(cp, current, cp.subparsed.filter(isPlaceholder));
            } else {
              var _localTags3, _cp$value2;
              var sizeScope = getScopeSize(part, current.parents);
              localTags = getLocalTags(tags, current.path, sizeScope);
              (_localTags3 = localTags)[_cp$value2 = cp.value] || (_localTags3[_cp$value2] = {});
            }
          }
        }
        continue;
      }
      if (part.dataBound === false) {
        continue;
      }
      (_localTags4 = localTags)[_part$value2 = part.value] || (_localTags4[_part$value2] = {});
    }
  }
  return tags;
}
module.exports = {
  getTags: getTags,
  isPlaceholder: isPlaceholder
};