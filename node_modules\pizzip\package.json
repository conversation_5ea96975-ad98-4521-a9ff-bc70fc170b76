{"name": "pizzip", "version": "3.2.0", "author": "<PERSON>", "description": "Create, read and edit .zip files synchronously with Javascript", "scripts": {"lint:fix": "npm run prettier:fix && npm run eslint:fix", "eslint:fix": "eslint --cache 'es6/**/*.js' --fix", "prettier:fix": "prettier --cache --write '*.js' 'es6/**/*.js' 'test/test.js' test/node.js 'utils/*.js' 'utils/es6/**/*.js' 'documentation/*/*.md'", "browserify:lib": "webpack", "babel": "babel es6 --out-dir js", "babel:watch": "npm run babel -- --watch", "build": "npm run browserify:lib && npm run browserify:lib:min", "browserify:lib:min": "cross-env MIN=true webpack", "preversion": "npm run babel && npm run build && npm test && npm run utils:build && cp utils/dist/* dist && npm run test:typings", "test": "mocha test/test.js", "test:watch": "mocha test/test.js --watch", "test:typings": "cp es6/*.ts js && tsd js && tsd utils", "utils:build": "cd utils && cross-env MIN=true ../node_modules/.bin/webpack && ../node_modules/.bin/webpack && IE=true cross-env MIN=true ../node_modules/.bin/webpack && IE=true ../node_modules/.bin/webpack"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "yim<PERSON><PERSON>"}, {"name": "<PERSON>"}], "main": "./js/index.js", "repository": {"type": "git", "url": "https://github.com/open-xml-templating/pizzip"}, "keywords": ["zip", "deflate", "inflate"], "devDependencies": {"@babel/cli": "^7.27.1", "@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.27.1", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/preset-env": "^7.27.1", "acorn": "^8.14.1", "babel-loader": "^10.0.0", "chai": "^5.2.0", "cross-env": "^7.0.3", "eslint": "^9.26.0", "eslint_d": "^14.3.0", "eslint-plugin-import": "^2.31.0", "globals": "^16.0.0", "mocha": "^11.2.2", "prettier": "^3.5.3", "tsd": "^0.32.0", "webpack": "^5.99.7", "webpack-cli": "^6.0.1"}, "dependencies": {"pako": "^2.1.0"}, "license": "(MIT OR GPL-3.0)"}