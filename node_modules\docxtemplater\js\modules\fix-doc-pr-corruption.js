"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var _require = require("../doc-utils.js"),
  setSingleAttribute = _require.setSingleAttribute,
  isTagStart = _require.isTagStart;

/*
 * We use a class here because this object is storing "state" in this.Lexer,
 * this.zip, this.xmlDocuments
 *
 * In version 3.34.3 and before, the state could be overwritten if the module
 * was attached to two docxtemplater instances
 *
 * Now, since the module will be cloned if already attached, it should work
 * correctly even on multiple instances in parallel
 */
var FixDocPRCorruptionModule = /*#__PURE__*/function () {
  function FixDocPRCorruptionModule() {
    _classCallCheck(this, FixDocPRCorruptionModule);
    this.name = "FixDocPRCorruptionModule";
  }
  return _createClass(FixDocPRCorruptionModule, [{
    key: "clone",
    value: function clone() {
      return new FixDocPRCorruptionModule();
    }
  }, {
    key: "set",
    value: function set(options) {
      if (options.Lexer) {
        this.Lexer = options.Lexer;
      }
      if (options.zip) {
        this.zip = options.zip;
      }
      if (options.xmlDocuments) {
        this.xmlDocuments = options.xmlDocuments;
      }
    }
  }, {
    key: "on",
    value: function on(event) {
      // Stryker disable all : because this is an optimisation that won't make any tests fail
      if (event !== "syncing-zip") {
        return;
      }
      this.attached = false;
      // Stryker restore all
      var zip = this.zip;
      var Lexer = this.Lexer;
      var prId = 1;
      for (var _i2 = 0, _zip$file2 = zip.file(/.xml$/); _i2 < _zip$file2.length; _i2++) {
        var f = _zip$file2[_i2];
        var xmlDoc = this.xmlDocuments[f.name];
        if (xmlDoc) {
          for (var _i4 = 0, _xmlDoc$getElementsBy2 = xmlDoc.getElementsByTagName("wp:docPr"); _i4 < _xmlDoc$getElementsBy2.length; _i4++) {
            var pr = _xmlDoc$getElementsBy2[_i4];
            pr.setAttribute("id", prId++);
          }
          continue;
        }
        var text = f.asText();
        var xmllexed = Lexer.xmlparse(text, {
          text: [],
          other: ["wp:docPr"]
        });
        if (xmllexed.length > 1) {
          /* eslint-disable-next-line no-loop-func */
          text = xmllexed.reduce(function (fullText, part) {
            if (isTagStart("wp:docPr", part)) {
              return fullText + setSingleAttribute(part.value, "id", prId++);
            }
            return fullText + part.value;
          }, "");
        }
        zip.file(f.name, text);
      }
    }
  }]);
}();
module.exports = new FixDocPRCorruptionModule();