"use strict";

var DataReader = require("./dataReader.js");
var utils = require("./utils.js");
function StringReader(data, optimizedBinaryString) {
  this.data = data;
  if (!optimizedBinaryString) {
    this.data = utils.string2binary(this.data);
  }
  this.length = this.data.length;
  this.index = 0;
  this.zero = 0;
}
StringReader.prototype = new DataReader();
/**
 * @see DataReader.byteAt
 */
StringReader.prototype.byteAt = function (i) {
  return this.data.charCodeAt(this.zero + i);
};
/**
 * @see DataReader.lastIndexOfSignature
 */
StringReader.prototype.lastIndexOfSignature = function (sig) {
  return this.data.lastIndexOf(sig) - this.zero;
};
/**
 * @see DataReader.readData
 */
StringReader.prototype.readData = function (size) {
  this.checkOffset(size);
  // this will work because the constructor applied the "& 0xff" mask.
  var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);
  this.index += size;
  return result;
};
module.exports = StringReader;