"use strict";

var _require = require("../utils.js"),
  expect = _require.expect;
var mergesort = require("../../merge-sort.js");
describe("Mergesort", function () {
  it("should work with simple", function () {
    var sorted = mergesort([[{
      offset: 1
    }, {
      offset: 4
    }], [{
      offset: 2
    }, {
      offset: 3
    }]]);
    var offsets = sorted.map(function (_ref) {
      var offset = _ref.offset;
      return offset;
    });
    expect(offsets).to.deep.equal([1, 2, 3, 4]);
  });
  it("should work even when having first array be the smallest values", function () {
    /*
     * In previous versions, this simple example would fail with the
     * message : `Cannot read property 'offset' of undefined`
     */
    var sorted = mergesort([[{
      offset: 1
    }, {
      offset: 2
    }], [{
      offset: 4
    }, {
      offset: 5
    }], [{
      offset: 3
    }, {
      offset: 6
    }]]);
    var offsets = sorted.map(function (_ref2) {
      var offset = _ref2.offset;
      return offset;
    });
    expect(offsets).to.deep.equal([1, 2, 3, 4, 5, 6]);
  });
  it("should work for complex case with empty values", function () {
    var sorted = mergesort([[{
      offset: 2
    }, {
      offset: 6
    }], [{
      offset: 1
    }, {
      offset: 4
    }], [], [{
      offset: 7
    }, {
      offset: 8
    }], [], [], [{
      offset: 3
    }, {
      offset: 5
    }], []]);
    var offsets = sorted.map(function (_ref3) {
      var offset = _ref3.offset;
      return offset;
    });
    expect(offsets).to.deep.equal([1, 2, 3, 4, 5, 6, 7, 8]);
  });
});